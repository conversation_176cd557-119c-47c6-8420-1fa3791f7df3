'use client';

import { useEffect, useState } from 'react';
import { RefreshCw, Trash2, CheckCircle } from 'lucide-react';

export default function ClearCachePage() {
  const [cacheCleared, setCacheCleared] = useState(false);
  const [loading, setLoading] = useState(false);

  const clearAllCaches = async () => {
    setLoading(true);
    
    try {
      // Clear service worker
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        for (const registration of registrations) {
          await registration.unregister();
        }
        console.log('✅ Service worker unregistered');
      }

      // Clear all caches
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        for (const cacheName of cacheNames) {
          await caches.delete(cacheName);
        }
        console.log('✅ All caches cleared');
      }

      // Clear storage
      localStorage.clear();
      sessionStorage.clear();
      console.log('✅ Storage cleared');

      setCacheCleared(true);
      
      // Reload after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      
    } catch (error) {
      console.error('Error clearing caches:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Auto-clear caches if requested
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('auto') === 'true') {
      clearAllCaches();
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          {cacheCleared ? (
            <CheckCircle className="w-8 h-8 text-green-600" />
          ) : (
            <Trash2 className="w-8 h-8 text-blue-600" />
          )}
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          {cacheCleared ? 'Caches Cleared!' : 'Clear Browser Caches'}
        </h1>
        
        <p className="text-gray-600 mb-6 leading-relaxed">
          {cacheCleared 
            ? 'All browser caches have been cleared successfully. The page will reload automatically.'
            : 'This will clear all browser caches, service workers, and local storage to resolve any caching issues.'
          }
        </p>
        
        {!cacheCleared && (
          <button
            onClick={clearAllCaches}
            disabled={loading}
            className="w-full bg-gradient-to-r from-blue-600 to-green-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-700 hover:to-green-700 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50"
          >
            {loading ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
            {loading ? 'Clearing...' : 'Clear All Caches'}
          </button>
        )}
        
        <div className="mt-6 text-sm text-gray-500">
          <p>This will:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Unregister service workers</li>
            <li>Clear all browser caches</li>
            <li>Clear local & session storage</li>
            <li>Reload the page</li>
          </ul>
        </div>
        
        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-xs text-gray-400">
            Development Cache Clearing Tool
          </p>
        </div>
      </div>
    </div>
  );
}
