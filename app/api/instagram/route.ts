import { NextRequest, NextResponse } from 'next/server';

interface InstagramPost {
  id: string;
  caption: string;
  media_url: string;
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
  permalink: string;
  timestamp: string;
  like_count?: number;
  comments_count?: number;
}

// Cache for Instagram posts
let cachedPosts: InstagramPost[] | null = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour

export async function GET(request: NextRequest) {
  try {
    // Check cache first
    if (cachedPosts && Date.now() - cacheTimestamp < CACHE_DURATION) {
      return NextResponse.json(cachedPosts, {
        headers: {
          'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200',
        },
      });
    }

    // Check if Instagram API is configured
    const accessToken = process.env.INSTAGRAM_ACCESS_TOKEN;
    
    if (!accessToken) {
      console.warn('Instagram access token not configured, using fallback posts');
      return NextResponse.json(getFallbackPosts(), {
        headers: {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
        },
      });
    }

    // Fetch from Instagram Basic Display API
    const response = await fetch(
      `https://graph.instagram.com/me/media?fields=id,caption,media_url,media_type,permalink,timestamp&access_token=${accessToken}&limit=12`
    );

    if (!response.ok) {
      throw new Error(`Instagram API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Transform the data
    const posts: InstagramPost[] = data.data.map((post: any) => ({
      id: post.id,
      caption: post.caption || '',
      media_url: post.media_url,
      media_type: post.media_type,
      permalink: post.permalink,
      timestamp: post.timestamp,
    }));

    // Update cache
    cachedPosts = posts;
    cacheTimestamp = Date.now();

    return NextResponse.json(posts, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200',
      },
    });

  } catch (error) {
    console.error('Instagram API error:', error);
    
    // Return cached posts if available, otherwise fallback
    const postsToReturn = cachedPosts || getFallbackPosts();
    
    return NextResponse.json(postsToReturn, {
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
      },
    });
  }
}

// Fallback posts when Instagram API is not available
function getFallbackPosts(): InstagramPost[] {
  return [
    {
      id: 'fallback_1',
      caption: '🏔️ Amazing adventure in the Himalayas with our students! Experiencing the beauty of nature while learning about geography and environmental science. #EducationalTour #Adventure #Positive7 #LearningBeyondClassrooms',
      media_url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=600&fit=crop&crop=center',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date().toISOString(),
      like_count: 127,
      comments_count: 23
    },
    {
      id: 'fallback_2',
      caption: '🎒 Students exploring the rich heritage of Rajasthan! Learning about history, architecture, and culture through immersive experiences. #Heritage #Learning #Rajasthan #CulturalEducation',
      media_url: 'https://images.unsplash.com/photo-1524492412937-b28074a5d7da?w=600&h=600&fit=crop&crop=center',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 86400000).toISOString(),
      like_count: 89,
      comments_count: 15
    },
    {
      id: 'fallback_3',
      caption: '🌊 Beach cleanup drive combined with marine biology learning! Our students are making a difference while understanding ocean ecosystems. #Environment #Education #SocialImpact #MarineBiology',
      media_url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=600&h=600&fit=crop&crop=center',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 172800000).toISOString(),
      like_count: 156,
      comments_count: 31
    },
    {
      id: 'fallback_4',
      caption: '🏛️ Exploring ancient architecture and history! Our students are amazed by the intricate craftsmanship and learning about historical construction techniques. #History #Architecture #AncientIndia',
      media_url: 'https://images.unsplash.com/photo-1564507592333-c60657eea523?w=600&h=600&fit=crop&crop=center',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 259200000).toISOString(),
      like_count: 203,
      comments_count: 42
    },
    {
      id: 'fallback_5',
      caption: '🌿 Nature walk and wildlife photography session! Connecting with nature through education and developing photography skills. #Wildlife #Photography #NatureEducation #SkillDevelopment',
      media_url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=600&h=600&fit=crop&crop=center',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 345600000).toISOString(),
      like_count: 174,
      comments_count: 28
    },
    {
      id: 'fallback_6',
      caption: '🎨 Art and culture workshop with local artisans! Hands-on learning at its best - students creating traditional crafts while understanding cultural heritage. #Art #Culture #Workshop #TraditionalCrafts',
      media_url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=600&h=600&fit=crop&crop=center',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 432000000).toISOString(),
      like_count: 145,
      comments_count: 19
    },
    {
      id: 'fallback_7',
      caption: '🚀 Science experiment session during our educational tour! Making learning fun and interactive with hands-on experiments. #Science #Education #STEM #InteractiveLearning',
      media_url: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=600&h=600&fit=crop&crop=center',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 518400000).toISOString(),
      like_count: 98,
      comments_count: 12
    },
    {
      id: 'fallback_8',
      caption: '🏕️ Camping under the stars! Teaching students about astronomy and outdoor survival skills. An unforgettable learning experience! #Camping #Astronomy #OutdoorEducation #StarGazing',
      media_url: 'https://images.unsplash.com/photo-1504851149312-7a075b496cc7?w=600&h=600&fit=crop&crop=center',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 604800000).toISOString(),
      like_count: 234,
      comments_count: 45
    }
  ];
}
