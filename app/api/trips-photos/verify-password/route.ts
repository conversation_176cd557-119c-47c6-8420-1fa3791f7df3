import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    const { albumId, password } = await request.json();

    if (!albumId || !password) {
      return NextResponse.json(
        { error: 'Album ID and password are required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Fetch the album details
    const { data: album, error } = await supabase
      .from('trip_photos_details')
      .select('access_password, google_drive_link')
      .eq('id', albumId)
      .single();

    if (error || !album) {
      return NextResponse.json(
        { error: 'Album not found' },
        { status: 404 }
      );
    }

    // Check if password matches
    if (album.access_password !== password) {
      return NextResponse.json(
        { error: 'Incorrect password' },
        { status: 401 }
      );
    }

    // Return the download link only if password is correct
    return NextResponse.json({
      success: true,
      downloadLink: album.google_drive_link || '#'
    });

  } catch (error) {
    console.error('Password verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
