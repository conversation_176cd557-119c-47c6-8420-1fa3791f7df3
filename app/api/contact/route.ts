import { NextRequest, NextResponse } from 'next/server';
import { createServer<PERSON>up<PERSON>se } from '@/lib/supabase-server';
import { sendInquiryNotification, InquiryEmailData } from '@/lib/email';
import { contactFormSchema, validateData } from '@/lib/validation-schemas';
import { withRateLimit, rateLimiters } from '@/lib/rate-limiter';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// POST /api/contact - Submit contact form with enhanced validation and rate limiting
async function handleContactSubmission(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input data using Zod schema
    const validation = validateData(contactFormSchema, body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const validatedData = validation.data;

    // Additional security checks
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /data:text\/html/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];

    const textFields = [validatedData.name, validatedData.subject, validatedData.message];
    const hasSuspiciousContent = textFields.some(field => 
      suspiciousPatterns.some(pattern => pattern.test(field))
    );

    if (hasSuspiciousContent) {
      console.warn('Suspicious content detected in contact form:', {
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date().toISOString()
      });
      
      return NextResponse.json(
        { error: 'Invalid content detected' },
        { status: 400 }
      );
    }

    // Check for spam patterns
    const spamIndicators = [
      /\b(viagra|cialis|casino|lottery|winner|congratulations)\b/i,
      /\b(click here|act now|limited time|urgent)\b/i,
      /\$\$\$|\b\d+\s*%\s*off\b/i,
      /\b(free money|make money|work from home)\b/i
    ];

    const hasSpamContent = textFields.some(field => 
      spamIndicators.some(pattern => pattern.test(field))
    );

    if (hasSpamContent) {
      console.warn('Potential spam detected in contact form:', {
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        email: validatedData.email,
        timestamp: new Date().toISOString()
      });
      
      // Return success to avoid revealing spam detection
      return NextResponse.json({
        message: 'Thank you for your message. We will get back to you soon!'
      }, { status: 200 });
    }

    const supabase = createServerSupabase();

    // Check for duplicate submissions (same email and message in last 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
    
    const { data: recentSubmissions } = await supabase
      .from('inquiries')
      .select('id')
      .eq('email', validatedData.email)
      .eq('message', validatedData.message)
      .gte('created_at', fiveMinutesAgo);

    if (recentSubmissions && recentSubmissions.length > 0) {
      return NextResponse.json(
        { error: 'Duplicate submission detected. Please wait before submitting again.' },
        { status: 429 }
      );
    }

    // Save inquiry to database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .insert({
        name: validatedData.name,
        email: validatedData.email,
        phone: validatedData.phone,
        subject: validatedData.subject,
        message: validatedData.message,
        inquiry_type: 'Contact Form',
        status: 'new',
        metadata: {
          source: 'contact_form',
          ip: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          timestamp: new Date().toISOString()
        }
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving contact form to database:', dbError);
      return NextResponse.json(
        { error: 'Failed to submit contact form. Please try again.' },
        { status: 500 }
      );
    }

    // Log successful submission
    console.log('📧 CONTACT FORM SUBMITTED:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('Name:', inquiry.name);
    console.log('Email:', inquiry.email);
    console.log('Subject:', inquiry.subject);
    console.log('Message:', inquiry.message.substring(0, 100) + '...');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Send email notification
    try {
      const emailData: InquiryEmailData = {
        id: inquiry.id,
        name: inquiry.name,
        email: inquiry.email,
        phone: inquiry.phone || undefined,
        subject: inquiry.subject || undefined,
        message: inquiry.message,
        inquiry_type: 'Contact Form',
        created_at: inquiry.created_at || new Date().toISOString()
      };
      
      await sendInquiryNotification(emailData);
      console.log('✅ Email notification sent successfully');
    } catch (emailError) {
      console.error('❌ Error sending email notification:', emailError);
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      message: 'Thank you for your message! We will get back to you within 24 hours.',
      data: {
        id: inquiry.id,
        name: inquiry.name,
        email: inquiry.email,
        subject: inquiry.subject,
        created_at: inquiry.created_at
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Error in POST /api/contact:', error);
    
    // Log error details for debugging
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
    
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the contact form endpoint
export const POST = withRateLimit(handleContactSubmission, rateLimiters.contact);

// GET method for health check
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    endpoint: 'contact',
    timestamp: new Date().toISOString()
  });
}
