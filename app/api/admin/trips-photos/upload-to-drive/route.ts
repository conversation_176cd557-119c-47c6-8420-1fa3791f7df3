import { NextRequest, NextResponse } from 'next/server';
import { uploadToGoogleDrive, validateFolderAccess } from '@/lib/google-drive';
import path from 'path';
import fs from 'fs-extra';
import { createServerSupabase } from '@/lib/supabase-server';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folderId = formData.get('folderId') as string;
    const tripPhotoId = formData.get('tripPhotoId') as string;

    if (!file || !folderId || !tripPhotoId) {
      return NextResponse.json(
        { error: 'Missing required fields: file, folderId, or tripPhotoId' },
        { status: 400 }
      );
    }

    console.log(`[UPLOAD] Starting upload process for trip photo ID: ${tripPhotoId}`);
    console.log(`[UPLOAD] File: ${file.name}, Size: ${file.size} bytes`);
    console.log(`[UPLOAD] Target folder ID: ${folderId}`);

    // Validate folder access first
    const hasAccess = await validateFolderAccess(`https://drive.google.com/drive/folders/${folderId}`);
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Cannot access the specified Google Drive folder. Please check folder permissions.' },
        { status: 403 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Create temporary file path
    const tempDir = path.join(process.cwd(), 'temp');
    await fs.ensureDir(tempDir);
    
    const tempFilePath = path.join(tempDir, `${Date.now()}_${file.name}`);
    
    try {
      // Write buffer to temporary file
      await fs.writeFile(tempFilePath, buffer);
      
      // Upload to Google Drive
      const driveFileId = await uploadToGoogleDrive(
        tempFilePath,
        file.name,
        file.type,
        folderId
      );

      console.log(`[UPLOAD] Successfully uploaded to Google Drive with ID: ${driveFileId}`);

      // Update trip photo details in database
      const supabase = createServerSupabase();
      const { error: updateError } = await supabase
        .from('trip_photos_details')
        .update({
          google_drive_file_id: driveFileId,
          updated_at: new Date().toISOString()
        })
        .eq('id', tripPhotoId);

      if (updateError) {
        console.error('[UPLOAD] Error updating database:', updateError);
        return NextResponse.json(
          { error: 'Upload successful but failed to update database' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'File uploaded successfully to Google Drive',
        driveFileId,
        fileName: file.name
      });

    } finally {
      // Clean up temporary file
      try {
        await fs.remove(tempFilePath);
        console.log(`[UPLOAD] Cleaned up temporary file: ${tempFilePath}`);
      } catch (cleanupError) {
        console.warn(`[UPLOAD] Failed to clean up temporary file: ${cleanupError}`);
      }
    }

  } catch (error) {
    console.error('[UPLOAD] Upload failed:', error);
    
    // Check if it's the specific OpenSSL error
    if (error instanceof Error && error.message.includes('DECODER routines::unsupported')) {
      return NextResponse.json(
        { 
          error: 'Google Drive authentication failed due to OpenSSL compatibility issue. Please contact administrator.',
          details: 'The service account key is incompatible with the current Node.js version.'
        },
        { status: 503 }
      );
    }
    
    return NextResponse.json(
      { error: 'Upload failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
