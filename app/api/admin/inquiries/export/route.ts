import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/inquiries/export - Export inquiries as CSV with date filters
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse date filter parameter
    const dateFilter = searchParams.get('dateFilter') || 'all'; // 'today', 'month', 'all'
    
    // Build query
    let query = supabase
      .from('inquiries')
      .select('*, trips(title)')
      .order('created_at', { ascending: false });

    // Apply date filters
    const now = new Date();
    if (dateFilter === 'today') {
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      query = query.gte('created_at', today.toISOString());
    } else if (dateFilter === 'month') {
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      query = query.gte('created_at', firstDayOfMonth.toISOString());
    }

    const { data: inquiries, error } = await query;

    if (error) {
      console.error('Error fetching inquiries for export:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    // Generate CSV content
    const csvHeaders = [
      'ID',
      'Name',
      'Email',
      'Phone',
      'Subject',
      'Message',
      'Inquiry Type',
      'Trip',
      'Status',
      'Created At',
      'Admin Notes'
    ];

    const csvRows = inquiries?.map(inquiry => [
      inquiry.id,
      `"${inquiry.name || ''}"`,
      `"${inquiry.email || ''}"`,
      `"${inquiry.phone || ''}"`,
      `"${inquiry.subject || ''}"`,
      `"${(inquiry.message || '').replace(/"/g, '""')}"`, // Escape quotes in message
      `"${inquiry.inquiry_type || ''}"`,
      `"${inquiry.trips?.title || ''}"`,
      `"${inquiry.status || ''}"`,
      `"${new Date(inquiry.created_at || '').toLocaleString()}"`,
      `"${(inquiry.admin_notes || '').replace(/"/g, '""')}"` // Escape quotes in admin notes
    ]) || [];

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.join(','))
    ].join('\n');

    // Generate filename with date filter
    const dateStr = new Date().toISOString().split('T')[0];
    const filename = `inquiries_${dateFilter}_${dateStr}.csv`;

    // Return CSV file
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-store, max-age=0'
      }
    });

  } catch (error) {
    console.error('Error in inquiries export:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
