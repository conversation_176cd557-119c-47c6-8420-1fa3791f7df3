import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminSupabaseClient } from '@/lib/auth-server';

// GET /api/admin/team-members/[id] - Get single team member (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('team_members', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const teamMemberId = resolvedParams.id;

    if (!teamMemberId) {
      return NextResponse.json(
        { error: 'Team member ID is required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminSupabaseClient();

    const { data: teamMember, error } = await adminSupabase
      .from('team_members')
      .select('*')
      .eq('id', teamMemberId)
      .single();

    if (error) {
      console.error('Error fetching team member:', error);
      return NextResponse.json(
        { error: 'Team member not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ teamMember });

  } catch (error: any) {
    console.error('Get team member error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team member', details: error.message },
      { status: 500 }
    );
  }
}

// PUT /api/admin/team-members/[id] - Update team member (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('team_members', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const teamMemberId = resolvedParams.id;

    if (!teamMemberId) {
      return NextResponse.json(
        { error: 'Team member ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, position, bio, image_url, sort_order, is_active } = body;

    // Validate required fields
    if (!name || !position || !bio) {
      return NextResponse.json(
        { error: 'Name, position, and bio are required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminSupabaseClient();

    const updateData: any = {
      name,
      position,
      bio,
      updated_at: new Date().toISOString(),
    };

    // Only update fields that are provided
    if (image_url !== undefined) updateData.image_url = image_url;
    if (sort_order !== undefined) updateData.sort_order = sort_order;
    if (is_active !== undefined) updateData.is_active = is_active;

    const { data: teamMember, error } = await adminSupabase
      .from('team_members')
      .update(updateData)
      .eq('id', teamMemberId)
      .select()
      .single();

    if (error) {
      console.error('Error updating team member:', error);
      return NextResponse.json(
        { error: 'Failed to update team member' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Team member updated successfully',
      teamMember,
    });

  } catch (error: any) {
    console.error('Update team member error:', error);
    return NextResponse.json(
      { error: 'Failed to update team member', details: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/team-members/[id] - Delete team member (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('team_members', 'delete');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const teamMemberId = resolvedParams.id;

    if (!teamMemberId) {
      return NextResponse.json(
        { error: 'Team member ID is required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminSupabaseClient();

    const { error } = await adminSupabase
      .from('team_members')
      .delete()
      .eq('id', teamMemberId);

    if (error) {
      console.error('Error deleting team member:', error);
      return NextResponse.json(
        { error: 'Failed to delete team member' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Team member deleted successfully',
    });

  } catch (error: any) {
    console.error('Delete team member error:', error);
    return NextResponse.json(
      { error: 'Failed to delete team member', details: error.message },
      { status: 500 }
    );
  }
}
