import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminSupabaseClient } from '@/lib/auth-server';

// GET /api/admin/team-members - Get all team members (admin only)
export async function GET(request: NextRequest) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('team_members', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const isActive = searchParams.get('isActive');

    const adminSupabase = createAdminSupabaseClient();

    // Build query
    let query = adminSupabase
      .from('team_members')
      .select('*', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,position.ilike.%${search}%`);
    }

    if (isActive !== null && isActive !== undefined) {
      query = query.eq('is_active', isActive === 'true');
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by sort_order, then by name
    query = query.order('sort_order', { ascending: true, nullsFirst: false })
                 .order('name', { ascending: true });

    const { data: teamMembers, error, count } = await query;

    if (error) {
      console.error('Error fetching team members:', error);
      return NextResponse.json(
        { error: 'Failed to fetch team members' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      teamMembers: teamMembers || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    });

  } catch (error: any) {
    console.error('Get team members error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team members', details: error.message },
      { status: 500 }
    );
  }
}

// POST /api/admin/team-members - Create new team member (admin only)
export async function POST(request: NextRequest) {
  try {
    const { user, hasAccess } = await verifyAdminAccess('team_members', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, position, bio, image_url, sort_order, is_active = true } = body;

    // Validate required fields
    if (!name || !position || !bio) {
      return NextResponse.json(
        { error: 'Name, position, and bio are required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminSupabaseClient();

    // If no sort_order provided, get the next available order
    let finalSortOrder = sort_order;
    if (!finalSortOrder) {
      const { data: maxOrderData } = await adminSupabase
        .from('team_members')
        .select('sort_order')
        .order('sort_order', { ascending: false, nullsFirst: false })
        .limit(1);
      
      finalSortOrder = (maxOrderData?.[0]?.sort_order || 0) + 1;
    }

    const { data: teamMember, error } = await adminSupabase
      .from('team_members')
      .insert({
        name,
        position,
        bio,
        image_url,
        sort_order: finalSortOrder,
        is_active,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating team member:', error);
      return NextResponse.json(
        { error: 'Failed to create team member' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Team member created successfully',
      teamMember,
    });

  } catch (error: any) {
    console.error('Create team member error:', error);
    return NextResponse.json(
      { error: 'Failed to create team member', details: error.message },
      { status: 500 }
    );
  }
}
