import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { verifyAdminAuth } from '@/lib/auth-helpers';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { adminUser, hasPermission } = authResult;

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Permission function not available' },
        { status: 500 }
      );
    }
    const supabase = createServerSupabase();

    // Initialize stats object
    const stats: any = {
      totalTrips: 0,
      activeTrips: 0,
      draftTrips: 0,
      totalBlogs: 0,
      publishedBlogs: 0,
      draftBlogs: 0,
      totalInquiries: 0,
      newInquiries: 0,
      respondedInquiries: 0,
      totalPhotos: 0,
      recentTrips: [],
      recentBlogs: [],
      recentInquiries: []
    };

    // Fetch trips data if user has permission
    if (hasPermission('trips', 'read')) {
      const [tripsResult, activeTripsResult, draftTripsResult, recentTripsResult] = await Promise.all([
        supabase.from('trips').select('id', { count: 'exact', head: true }),
        supabase.from('trips').select('id', { count: 'exact', head: true }).eq('is_active', true),
        supabase.from('trips').select('id', { count: 'exact', head: true }).eq('is_active', false),
        supabase
          .from('trips')
          .select('id, title, slug, destination, created_at, is_active')
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      stats.totalTrips = tripsResult.count || 0;
      stats.activeTrips = activeTripsResult.count || 0;
      stats.draftTrips = draftTripsResult.count || 0;
      stats.recentTrips = recentTripsResult.data || [];
    }

    // Fetch blogs data if user has permission
    if (hasPermission('blog', 'read')) {
      const [blogsResult, publishedBlogsResult, draftBlogsResult, recentBlogsResult] = await Promise.all([
        supabase.from('blog_posts').select('id', { count: 'exact', head: true }),
        supabase.from('blog_posts').select('id', { count: 'exact', head: true }).eq('is_published', true),
        supabase.from('blog_posts').select('id', { count: 'exact', head: true }).eq('is_published', false),
        supabase
          .from('blog_posts')
          .select('id, title, slug, created_at, is_published')
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      stats.totalBlogs = blogsResult.count || 0;
      stats.publishedBlogs = publishedBlogsResult.count || 0;
      stats.draftBlogs = draftBlogsResult.count || 0;
      stats.recentBlogs = recentBlogsResult.data || [];
    }

    // Fetch inquiries data if user has permission
    if (hasPermission('inquiries', 'read')) {
      const [inquiriesResult, newInquiriesResult, respondedInquiriesResult, recentInquiriesResult] = await Promise.all([
        supabase.from('inquiries').select('id', { count: 'exact', head: true }),
        supabase.from('inquiries').select('id', { count: 'exact', head: true }).eq('status', 'new'),
        supabase.from('inquiries').select('id', { count: 'exact', head: true }).eq('status', 'resolved'),
        supabase
          .from('inquiries')
          .select('id, name, email, subject, status, created_at')
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      stats.totalInquiries = inquiriesResult.count || 0;
      stats.newInquiries = newInquiriesResult.count || 0;
      stats.respondedInquiries = respondedInquiriesResult.count || 0;
      stats.recentInquiries = recentInquiriesResult.data || [];
    }

    // Fetch photos data if user has permission
    if (hasPermission('photos', 'read')) {
      const photosResult = await supabase
        .from('trip_photos_details')
        .select('id', { count: 'exact', head: true });
      
      stats.totalPhotos = photosResult.count || 0;
    }

    return NextResponse.json(stats, {
      headers: {
        'Cache-Control': 'private, no-cache, no-store, must-revalidate',
      },
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}
