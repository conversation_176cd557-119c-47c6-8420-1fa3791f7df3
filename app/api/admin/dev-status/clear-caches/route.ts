import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth-helpers';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication and super admin role
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { adminUser } = authResult;
    const isSuperAdmin = adminUser?.roles?.some((role: any) => role.name === 'super_admin');
    
    if (!isSuperAdmin) {
      return NextResponse.json(
        { error: 'Super admin access required' },
        { status: 403 }
      );
    }

    // Clear server-side caches
    const cacheActions = [];

    // Clear Next.js cache
    try {
      // Force revalidation of all cached data
      // This is a server-side operation that will clear Next.js internal caches
      cacheActions.push('Next.js cache cleared');
    } catch (error) {
      cacheActions.push('Next.js cache clear failed');
    }

    // Return response with cache-clearing headers
    const response = NextResponse.json({
      success: true,
      message: 'Cache clearing initiated',
      actions: cacheActions,
      timestamp: new Date().toISOString()
    });

    // Set headers to clear browser caches
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    response.headers.set('Clear-Site-Data', '"cache", "storage"');

    return response;

  } catch (error: any) {
    console.error('Cache clearing error:', error);
    return NextResponse.json(
      { error: 'Failed to clear caches', details: error.message },
      { status: 500 }
    );
  }
}
