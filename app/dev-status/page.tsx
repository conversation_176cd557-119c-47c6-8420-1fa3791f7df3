'use client';

import { useEffect, useState } from 'react';
import { CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react';

interface StatusCheck {
  name: string;
  status: 'success' | 'error' | 'warning' | 'loading';
  message: string;
}

export default function DevStatusPage() {
  const [checks, setChecks] = useState<StatusCheck[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    runStatusChecks();
  }, []);

  const runStatusChecks = async () => {
    setLoading(true);
    const newChecks: StatusCheck[] = [];

    // Check fonts
    try {
      if (document.fonts) {
        await document.fonts.ready;
        const fontsLoaded = [
          document.fonts.check('16px Inter'),
          document.fonts.check('16px Poppins'),
          document.fonts.check('16px Montserrat')
        ];
        
        if (fontsLoaded.every(Boolean)) {
          newChecks.push({
            name: 'Font Loading',
            status: 'success',
            message: 'All fonts (Inter, Poppins, Montserrat) loaded successfully'
          });
        } else {
          newChecks.push({
            name: 'Font Loading',
            status: 'warning',
            message: 'Some fonts may not be loaded properly'
          });
        }
      }
    } catch (error) {
      newChecks.push({
        name: 'Font Loading',
        status: 'error',
        message: `Font loading error: ${error}`
      });
    }

    // Check API endpoints
    const apiChecks = [
      { endpoint: '/api/auth/user', name: 'Auth API' },
      { endpoint: '/api/instagram', name: 'Instagram API' },
      { endpoint: '/api/trips', name: 'Trips API' },
    ];

    for (const { endpoint, name } of apiChecks) {
      try {
        const response = await fetch(endpoint);
        newChecks.push({
          name,
          status: response.ok ? 'success' : 'warning',
          message: `${endpoint} - Status: ${response.status}`
        });
      } catch (error) {
        newChecks.push({
          name,
          status: 'error',
          message: `${endpoint} - Error: ${error}`
        });
      }
    }

    // Check service worker
    if ('serviceWorker' in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        newChecks.push({
          name: 'Service Worker',
          status: registrations.length > 0 ? 'success' : 'warning',
          message: registrations.length > 0 
            ? `${registrations.length} service worker(s) registered`
            : 'No service workers registered'
        });
      } catch (error) {
        newChecks.push({
          name: 'Service Worker',
          status: 'error',
          message: `Service worker error: ${error}`
        });
      }
    }

    // Check caches
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        newChecks.push({
          name: 'Browser Caches',
          status: 'success',
          message: `${cacheNames.length} cache(s) found: ${cacheNames.join(', ')}`
        });
      } catch (error) {
        newChecks.push({
          name: 'Browser Caches',
          status: 'error',
          message: `Cache error: ${error}`
        });
      }
    }

    // Check console errors
    const errorCount = (window as any).__errorCount || 0;
    newChecks.push({
      name: 'Console Errors',
      status: errorCount === 0 ? 'success' : 'warning',
      message: errorCount === 0 ? 'No console errors detected' : `${errorCount} console errors detected`
    });

    setChecks(newChecks);
    setLoading(false);
  };

  const getStatusIcon = (status: StatusCheck['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'loading':
        return <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />;
    }
  };

  const getStatusColor = (status: StatusCheck['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'loading':
        return 'border-blue-200 bg-blue-50';
    }
  };

  if (process.env.NODE_ENV !== 'development') {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Development Only</h1>
          <p className="text-gray-600">This page is only available in development mode.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Development Status</h1>
            <button
              onClick={runStatusChecks}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>

          <div className="space-y-4">
            {checks.map((check, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 ${getStatusColor(check.status)}`}
              >
                <div className="flex items-start gap-3">
                  {getStatusIcon(check.status)}
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{check.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{check.message}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {loading && (
            <div className="text-center py-8">
              <RefreshCw className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Running status checks...</p>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/clear-cache"
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <h3 className="font-semibold text-gray-900">Clear Caches</h3>
              <p className="text-sm text-gray-600 mt-1">Clear all browser caches and reload</p>
            </a>
            
            <a
              href="/admin"
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <h3 className="font-semibold text-gray-900">Admin Panel</h3>
              <p className="text-sm text-gray-600 mt-1">Access the admin dashboard</p>
            </a>
            
            <a
              href="/"
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
            >
              <h3 className="font-semibold text-gray-900">Main Site</h3>
              <p className="text-sm text-gray-600 mt-1">Go to the main website</p>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
