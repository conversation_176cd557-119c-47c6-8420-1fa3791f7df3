import type { Metadata } from 'next/types'
import { notFound } from 'next/navigation'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import BlogPostClient from '@/components/blog/BlogPostClient'
import { createServerSupabase } from '@/lib/supabase-server'

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic'
export const revalidate = 0

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

// Generate metadata for the blog post
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  const supabase = createServerSupabase()
  
  const { data: post } = await supabase
    .from('blog_posts')
    .select('title, excerpt, featured_image_url, created_at, author_id')
    .eq('slug', slug)
    .eq('is_published', true)
    .single()

  if (!post) {
    return {
      title: 'Blog Post Not Found - Positive7',
      description: 'The requested blog post could not be found.'
    }
  }

  return {
    title: `${post.title} - Positive7 Blog`,
    description: post.excerpt || `Read ${post.title} on Positive7 blog`,
    keywords: `${post.title}, Positive7, educational tours, travel blog`,
    openGraph: {
      title: post.title,
      description: post.excerpt || '',
      images: post.featured_image_url ? [post.featured_image_url] : [],
      type: 'article',
      publishedTime: post.created_at || undefined
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt || '',
      images: post.featured_image_url ? [post.featured_image_url] : []
    }
  }
}

// Generate static params for all published blog posts
export async function generateStaticParams() {
  const supabase = createServerSupabase()
  
  const { data: posts } = await supabase
    .from('blog_posts')
    .select('slug')
    .eq('is_published', true)

  return posts?.map((post) => ({
    slug: post.slug,
  })) || []
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const supabase = createServerSupabase()
  
  // Fetch the blog post
  const { data: post, error } = await supabase
    .from('blog_posts')
    .select(`
      id,
      title,
      slug,
      content,
      excerpt,
      featured_image_url,
      author_id,
      created_at,
      updated_at,
      category,
      tags,
      published_at
    `)
    .eq('slug', slug)
    .eq('is_published', true)
    .single()

  if (error || !post) {
    notFound()
  }

  // Calculate reading time (rough estimate: 200 words per minute)
  const wordCount = post.content.split(/\s+/).length
  const readingTime = Math.ceil(wordCount / 200)

  // Use current date as fallback for created_at
  const currentDate = new Date().toISOString()

  // Adapt post data to match the client component's expected structure
  const adaptedPost = {
    ...post,
    author: post.author_id || 'Positive7 Team', // Default author name if author_id is null
    reading_time: readingTime,
    status: 'published', // Since we filtered by is_published=true
    excerpt: post.excerpt || '', // Ensure excerpt is never null
    featured_image_url: post.featured_image_url || undefined, // Convert null to undefined
    category: post.category || 'Uncategorized', // Ensure category is never null
    tags: post.tags || undefined, // Convert null to undefined
    created_at: post.created_at || currentDate, // Ensure created_at is never null
    updated_at: post.updated_at || currentDate, // Ensure updated_at is never null
  }

  // Fetch related posts
  const { data: relatedPostsData } = await supabase
    .from('blog_posts')
    .select(`
      id, 
      title, 
      slug, 
      excerpt, 
      featured_image_url, 
      author_id,
      created_at, 
      category
    `)
    .eq('is_published', true)
    .neq('id', post.id)
    .eq('category', post.category || 'Uncategorized')
    .limit(3)

  // Adapt related posts to match expected structure
  const relatedPosts = relatedPostsData?.map(post => ({
    ...post,
    author: post.author_id || 'Positive7 Team',
    excerpt: post.excerpt || '',
    reading_time: Math.ceil((post.excerpt?.split(/\s+/).length || 0) / 200),
    featured_image_url: post.featured_image_url || undefined,
    category: post.category || '',
    created_at: post.created_at || currentDate, // Ensure created_at is never null
  })) || []

  return (
    <>
      <Header />
      <main className="flex-1">
        <BlogPostClient 
          post={adaptedPost} 
          relatedPosts={relatedPosts} 
        />
      </main>
      <Footer />
    </>
  )
}
