'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import TripForm from '../components/Trip-FormCompleted';
import { TripFormData } from '@/types/trip';
import { useToast } from '@/hooks/useToast';

export default function NewTripPage() {
  const router = useRouter();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (data: TripFormData) => {
    setIsLoading(true);
    const toastId = toast.loading('Creating trip...');

    try {
      const response = await fetch('/api/admin/trips', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create trip');
      }

      toast.success('Trip created successfully!');
      router.push('/admin/trips');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create trip';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
      toast.dismiss(toastId);
    }
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <TripForm 
      onSubmit={handleSubmit} 
      isLoading={isLoading} 
      onCancel={() => router.back()}
    />
  );
} 