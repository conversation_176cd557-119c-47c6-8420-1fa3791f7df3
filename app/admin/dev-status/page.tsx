'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import AdminLayout from '@/components/layout/AdminLayout';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { CheckCircle, XCircle, AlertTriangle, RefreshCw, Play, Download, Code, Database, Cloud, Globe } from 'lucide-react';

interface StatusCheck {
  name: string;
  status: 'success' | 'error' | 'warning' | 'loading';
  message: string;
}

interface TestResult {
  name: string;
  status: boolean;
  details: string;
}

interface TestReport {
  timestamp: string;
  summary: {
    total: number;
    passed: number;
    failed: number;
    successRate: string;
  };
  tests: TestResult[];
  environment: {
    nodeVersion: string;
    platform: string;
    baseUrl: string;
  };
}

export default function AdminDevStatusPage() {
  const { user, hasPermission, loading: authLoading } = useAuth();
  const [checks, setChecks] = useState<StatusCheck[]>([]);
  const [loading, setLoading] = useState(true);
  const [testReport, setTestReport] = useState<TestReport | null>(null);
  const [runningTest, setRunningTest] = useState<string | null>(null);

  // Check if user is super admin
  const isSuperAdmin = user?.roles?.some(role => role.name === 'super_admin') || false;

  useEffect(() => {
    if (!authLoading && user) {
      runStatusChecks();
    }
  }, [authLoading, user]);

  const runStatusChecks = async () => {
    setLoading(true);
    const newChecks: StatusCheck[] = [];

    // Check fonts
    try {
      if (typeof window !== 'undefined' && document.fonts) {
        await document.fonts.ready;
        const fontsLoaded = [
          document.fonts.check('16px Inter'),
          document.fonts.check('16px Poppins'),
          document.fonts.check('16px Montserrat')
        ];
        
        if (fontsLoaded.every(Boolean)) {
          newChecks.push({
            name: 'Font Loading',
            status: 'success',
            message: 'All fonts (Inter, Poppins, Montserrat) loaded successfully'
          });
        } else {
          newChecks.push({
            name: 'Font Loading',
            status: 'warning',
            message: 'Some fonts may not be loaded properly'
          });
        }
      }
    } catch (error) {
      newChecks.push({
        name: 'Font Loading',
        status: 'error',
        message: `Font loading error: ${error}`
      });
    }

    // Check API endpoints
    const apiChecks = [
      { endpoint: '/api/auth/user', name: 'Auth API' },
      { endpoint: '/api/trips', name: 'Trips API' },
      { endpoint: '/api/blog', name: 'Blog API' },
      { endpoint: '/api/admin/dashboard', name: 'Admin Dashboard API' },
    ];

    for (const { endpoint, name } of apiChecks) {
      try {
        const response = await fetch(endpoint);
        newChecks.push({
          name,
          status: response.ok ? 'success' : 'warning',
          message: `${endpoint} - Status: ${response.status}`
        });
      } catch (error) {
        newChecks.push({
          name,
          status: 'error',
          message: `${endpoint} - Error: ${error}`
        });
      }
    }

    // Check service worker
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        newChecks.push({
          name: 'Service Worker',
          status: registrations.length > 0 ? 'success' : 'warning',
          message: registrations.length > 0 
            ? `${registrations.length} service worker(s) registered`
            : 'No service workers registered'
        });
      } catch (error) {
        newChecks.push({
          name: 'Service Worker',
          status: 'error',
          message: `Service worker error: ${error}`
        });
      }
    }

    // Check caches
    if (typeof window !== 'undefined' && 'caches' in window) {
      try {
        const cacheNames = await caches.keys();
        newChecks.push({
          name: 'Browser Caches',
          status: 'success',
          message: `${cacheNames.length} cache(s) found: ${cacheNames.join(', ')}`
        });
      } catch (error) {
        newChecks.push({
          name: 'Browser Caches',
          status: 'error',
          message: `Cache error: ${error}`
        });
      }
    }

    setChecks(newChecks);
    setLoading(false);
  };

  const runFunctionalityTest = async () => {
    setRunningTest('functionality');
    try {
      const response = await fetch('/api/admin/dev-status/run-functionality-test', {
        method: 'POST',
      });
      
      if (response.ok) {
        const report = await response.json();
        setTestReport(report);
      } else {
        console.error('Failed to run functionality test');
      }
    } catch (error) {
      console.error('Error running functionality test:', error);
    } finally {
      setRunningTest(null);
    }
  };

  const runApiAnalysis = async () => {
    setRunningTest('api-analysis');
    try {
      const response = await fetch('/api/admin/dev-status/run-api-analysis', {
        method: 'POST',
      });
      
      if (response.ok) {
        const report = await response.json();
        // Handle API analysis report
        console.log('API Analysis Report:', report);
      } else {
        console.error('Failed to run API analysis');
      }
    } catch (error) {
      console.error('Error running API analysis:', error);
    } finally {
      setRunningTest(null);
    }
  };

  const clearAllCaches = async () => {
    setRunningTest('clear-caches');
    try {
      const response = await fetch('/api/admin/dev-status/clear-caches', {
        method: 'POST',
      });
      
      if (response.ok) {
        // Refresh the page after clearing caches
        window.location.reload();
      } else {
        console.error('Failed to clear caches');
      }
    } catch (error) {
      console.error('Error clearing caches:', error);
    } finally {
      setRunningTest(null);
    }
  };

  const getStatusIcon = (status: StatusCheck['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'loading':
        return <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />;
    }
  };

  const getStatusColor = (status: StatusCheck['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'loading':
        return 'border-blue-200 bg-blue-50';
    }
  };

  if (authLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return null; // Will be redirected by middleware
  }

  if (!isSuperAdmin) {
    return (
      <AdminLayout>
        <div className="min-h-screen bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600">This page is only available to super administrators.</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Development Status</h1>
                <p className="text-gray-600 mt-1">System health monitoring and development tools</p>
              </div>
              <button
                onClick={runStatusChecks}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>

            <div className="space-y-4">
              {checks.map((check, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border-2 ${getStatusColor(check.status)}`}
                >
                  <div className="flex items-start gap-3">
                    {getStatusIcon(check.status)}
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{check.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{check.message}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {loading && (
              <div className="text-center py-8">
                <RefreshCw className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
                <p className="text-gray-600">Running status checks...</p>
              </div>
            )}
          </div>

          {/* Development Tools */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Development Tools</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Functionality Test */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <Database className="w-6 h-6 text-blue-600" />
                  <h3 className="font-semibold text-gray-900">Functionality Test</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Comprehensive test of all APIs, database connections, and external services.
                </p>
                <button
                  onClick={runFunctionalityTest}
                  disabled={runningTest === 'functionality'}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {runningTest === 'functionality' ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  {runningTest === 'functionality' ? 'Running...' : 'Run Test'}
                </button>
              </div>

              {/* API Analysis */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <Code className="w-6 h-6 text-green-600" />
                  <h3 className="font-semibold text-gray-900">API Analysis</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Analyze codebase for unused or orphaned API endpoints.
                </p>
                <button
                  onClick={runApiAnalysis}
                  disabled={runningTest === 'api-analysis'}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  {runningTest === 'api-analysis' ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  {runningTest === 'api-analysis' ? 'Analyzing...' : 'Run Analysis'}
                </button>
              </div>

              {/* Clear Caches */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <Cloud className="w-6 h-6 text-purple-600" />
                  <h3 className="font-semibold text-gray-900">Clear Caches</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Clear all application caches and force refresh.
                </p>
                <button
                  onClick={clearAllCaches}
                  disabled={runningTest === 'clear-caches'}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                >
                  {runningTest === 'clear-caches' ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <RefreshCw className="w-4 h-4" />
                  )}
                  {runningTest === 'clear-caches' ? 'Clearing...' : 'Clear Caches'}
                </button>
              </div>
            </div>
          </div>

          {/* Test Report */}
          {testReport && (
            <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Latest Test Report</h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{testReport.summary.total}</div>
                  <div className="text-sm text-gray-600">Total Tests</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{testReport.summary.passed}</div>
                  <div className="text-sm text-gray-600">Passed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{testReport.summary.failed}</div>
                  <div className="text-sm text-gray-600">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{testReport.summary.successRate}%</div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </div>
              </div>
              
              <div className="text-sm text-gray-500 mb-4">
                Generated: {new Date(testReport.timestamp).toLocaleString()}
              </div>
              
              <button
                onClick={() => {
                  const blob = new Blob([JSON.stringify(testReport, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `functionality-test-report-${new Date().toISOString().split('T')[0]}.json`;
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                <Download className="w-4 h-4" />
                Download Report
              </button>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
