'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import CloudinaryUpload from '@/components/ui/CloudinaryUpload';
import { TripPhotoDetails, TripPhotoDetailsFormData } from '@/types/trip-photos';

interface EditTripPhotoContentProps {
  tripPhotoDetails: TripPhotoDetails | null;
}

export default function EditTripPhotoContent({ tripPhotoDetails }: EditTripPhotoContentProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState<TripPhotoDetailsFormData>({
    trip_name: tripPhotoDetails?.trip_name || '',
    trip_description: tripPhotoDetails?.trip_description || '',
    featured_image_url: tripPhotoDetails?.featured_image_url || '',
    access_password: tripPhotoDetails?.access_password || '',
    google_drive_link: tripPhotoDetails?.google_drive_link || '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!tripPhotoDetails) return;
    
    if (!formData.trip_name) {
      setError('Trip name is required');
      return;
    }
    
    setError(null);
    setIsSaving(true);
    
    try {
      const response = await fetch(`/api/admin/trips-photos/${tripPhotoDetails.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to update trip photo album');
      }

      // Navigate back to detail page
      router.push(`/admin/trips-photos/${tripPhotoDetails.id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error && !tripPhotoDetails) {
    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">Trip photo album not found</p>
            <button 
              onClick={() => router.push('/admin/trips-photos')}
              className="mt-2 text-sm font-medium text-yellow-700 hover:text-yellow-600"
            >
              Back to Trip Photos
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!tripPhotoDetails) {
    return null;
  }

  return (
    <div>
      <div className="mb-8">
        <Link
          href={`/admin/trips-photos/${tripPhotoDetails.id}`}
          className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center w-fit mb-4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Album
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Edit {tripPhotoDetails.trip_name}</h1>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow sm:rounded-lg p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="trip_name" className="block text-sm font-medium text-gray-700">
              Trip Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="trip_name"
              name="trip_name"
              value={formData.trip_name}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
          
          <div>
            <label htmlFor="trip_description" className="block text-sm font-medium text-gray-700">
              Trip Description
            </label>
            <textarea
              id="trip_description"
              name="trip_description"
              value={formData.trip_description || ''}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Featured Image
            </label>
            <CloudinaryUpload
              onUpload={(url) => setFormData(prev => ({ ...prev, featured_image_url: url }))}
              currentImage={formData.featured_image_url || undefined}
              uploadType="trip"
              placeholder="Upload trip featured image"
            />
          </div>
          
          <div>
            <label htmlFor="access_password" className="block text-sm font-medium text-gray-700">
              Access Password (Optional)
            </label>
            <input
              type="text"
              id="access_password"
              name="access_password"
              value={formData.access_password || ''}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              placeholder="Leave blank for no password protection"
            />
            <p className="mt-1 text-xs text-gray-500">
              If provided, this password will be required to access the photos
            </p>
          </div>
          
          <div>
            <label htmlFor="google_drive_link" className="block text-sm font-medium text-gray-700">
              Google Drive/Photos Link
            </label>
            <input
              type="url"
              id="google_drive_link"
              name="google_drive_link"
              value={formData.google_drive_link || ''}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              placeholder="https://drive.google.com/..."
            />
            <p className="mt-1 text-xs text-gray-500">
              Link to Google Drive or Google Photos shared album
            </p>
          </div>
          
          <div className="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              onClick={() => router.push(`/admin/trips-photos/${tripPhotoDetails.id}`)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {isSaving ? (
                <>
                  <LoadingSpinner size="small" className="mr-2" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
} 