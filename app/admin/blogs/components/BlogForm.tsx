'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { BlogFormData } from '@/types/blog';
import { Calendar, Tag, Clock, User, Camera } from 'lucide-react';
import { getImageWithFallback } from '@/lib/image-fallbacks';
import CloudinaryUpload from '@/components/ui/CloudinaryUpload';
import { useToast } from '@/hooks/useToast';

interface BlogFormProps {
  initialData?: Partial<BlogFormData>;
  onSubmit: (data: BlogFormData) => Promise<void>;
  isLoading: boolean;
}

// Sample categories - in a real app, these might come from an API
const CATEGORIES = [
  'Travel',
  'Adventure',
  'Culture',
  'Food',
  'Lifestyle',
  'Nature',
  'Photography',
  'Tips & Tricks'
];

export default function BlogForm({ initialData, onSubmit, isLoading }: BlogFormProps) {
  const router = useRouter();
  const toast = useToast();
  const [formData, setFormData] = useState<BlogFormData>({
    title: '',
    content: '',
    excerpt: '',
    featured_image_url: '',
    category: '',
    tags: [],
    is_published: false,
    seo_title: '',
    seo_description: '',
    ...initialData,
  });

  const [error, setError] = useState<string | null>(null);
  const [tag, setTag] = useState('');
  const [showSeoSettings, setShowSeoSettings] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked,
    }));
  };

  const handleAddTag = () => {
    if (tag.trim()) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()],
      }));
      setTag('');
    }
  };

  const handleRemoveTag = (index: number) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    const toastId = toast.loading('Saving blog post...');

    try {
      await onSubmit(formData);
      toast.success('Blog post saved successfully!');
      router.push('/admin/blogs' as any);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save blog';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      toast.dismiss(toastId);
    }
  };

  const formatDate = () => {
    return new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const readingTime = Math.ceil(formData.content.length / 1000); // Rough estimate

  return (
    <div className="max-w-4xl mx-auto">
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-xl overflow-hidden shadow-lg">
        {/* Featured Image Section */}
        <div className="relative">
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Featured Image</h3>
            <CloudinaryUpload
              onUpload={(url) => setFormData(prev => ({ ...prev, featured_image_url: url }))}
              currentImage={formData.featured_image_url}
              uploadType="blog"
              placeholder="Upload blog featured image"
              className="h-80"
            />
          </div>
        </div>

        <div className="p-8">
          {/* Category Dropdown */}
          <div className="mb-6">
            <select
              name="category"
              id="category"
              value={formData.category}
              onChange={handleChange}
              className="block w-auto rounded-full px-4 py-2 text-sm font-medium bg-blue-100 text-blue-800 border-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Category</option>
              {CATEGORIES.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Title */}
          <div className="mb-6">
            <input
              type="text"
              name="title"
              id="title"
              required
              value={formData.title}
              onChange={handleChange}
              placeholder="Blog Title"
              className="block w-full border-0 p-0 text-4xl md:text-5xl font-bold text-gray-900 focus:ring-0 placeholder-gray-300"
            />
          </div>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 text-gray-500 text-sm mb-8">
            <div className="flex items-center">
              <User className="w-4 h-4 mr-2" />
              <span>By Admin</span>
            </div>
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span>{formatDate()}</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              <span>{readingTime} min read</span>
            </div>
          </div>

          {/* Excerpt */}
          <div className="mb-8">
            <textarea
              name="excerpt"
              id="excerpt"
              rows={2}
              value={formData.excerpt}
              onChange={handleChange}
              placeholder="Add a brief excerpt of your blog post"
              className="block w-full border-gray-200 rounded-md text-xl text-gray-600 leading-relaxed focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          {/* Content */}
          <div className="mb-8">
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Content</span>
                <span className="text-xs text-gray-500">
                  HTML is supported. Use &lt;h2&gt;, &lt;p&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;li&gt;, &lt;blockquote&gt;, &lt;a&gt;, &lt;strong&gt;, &lt;em&gt; tags.
                </span>
              </div>
              <textarea
                name="content"
                id="content"
                required
                rows={5}
                value={formData.content}
                onChange={handleChange}
                placeholder="Write your blog content here..."
                className="block w-full border-0 focus:ring-0 font-serif p-4"
              />
              <div className="border-t border-gray-200 bg-white p-6">
                <h4 className="text-xs uppercase tracking-wider text-gray-500 font-semibold mb-3">Preview</h4>
                <div className="prose prose-lg max-w-none" dangerouslySetInnerHTML={{ __html: formData.content || '<p class="text-gray-400 italic">Your content will appear here...</p>' }} />
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="mb-8 pt-8 border-t border-gray-200">
            <div className="flex flex-wrap items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Tags</h3>
              <div className="flex space-x-2">
                <input
                  type="text"
                  id="tag"
                  value={tag}
                  onChange={(e) => setTag(e.target.value)}
                  placeholder="Add a tag"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                />
                <Button
                  type="button"
                  onClick={handleAddTag}
                  variant="secondary"
                  size="sm"
                >
                  Add
                </Button>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 rounded-full bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-700"
                >
                  #{tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(index)}
                    className="ml-1 h-4 w-4 rounded-full bg-indigo-200 text-indigo-600 hover:bg-indigo-300 hover:text-indigo-700 flex items-center justify-center"
                  >
                    <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
            </div>
          </div>

          {/* SEO Settings Toggle */}
          <div className="mb-8">
            <button
              type="button"
              onClick={() => setShowSeoSettings(!showSeoSettings)}
              className="flex items-center text-indigo-600 hover:text-indigo-800"
            >
              <span className="mr-2">{showSeoSettings ? 'Hide' : 'Show'} SEO Settings</span>
              <svg className={`h-5 w-5 transform ${showSeoSettings ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            {showSeoSettings && (
              <div className="mt-4 space-y-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <label htmlFor="seo_title" className="block text-sm font-medium text-gray-700">
                    SEO Title
                  </label>
                  <input
                    type="text"
                    name="seo_title"
                    id="seo_title"
                    value={formData.seo_title}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>

                <div>
                  <label htmlFor="seo_description" className="block text-sm font-medium text-gray-700">
                    SEO Description
                  </label>
                  <textarea
                    name="seo_description"
                    id="seo_description"
                    rows={3}
                    value={formData.seo_description}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                  />
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center">
              <input
                type="checkbox"
                name="is_published"
                id="is_published"
                checked={formData.is_published}
                onChange={handleCheckboxChange}
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <label htmlFor="is_published" className="ml-2 block text-sm text-gray-900">
                Publish this blog post
              </label>
            </div>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="secondary"
                onClick={() => router.push('/admin/blogs' as any)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size="small" />
                    <span className="ml-2">Saving...</span>
                  </>
                ) : (
                  'Save Blog Post'
                )}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
} 