'use client';

import {
  Map,
  FileText,
  MessageSquare,
  Users,
  TrendingUp,
  Calendar,
  CheckCircle,
  Shield,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/lib/hooks/useAuth';
import { useToast } from '@/hooks/useToast';
import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import AdminLayout from '@/components/layout/AdminLayout';
import { DashboardSkeleton } from '@/components/ui/SkeletonLoader';

interface DashboardStats {
  // Content Stats
  totalTrips: number;
  activeTrips: number;
  draftTrips: number;
  totalBlogs: number;
  publishedBlogs: number;
  draftBlogs: number;

  // Inquiry Stats
  totalInquiries: number;
  newInquiries: number;
  respondedInquiries: number;

  // User Stats (for super admin)
  totalAdminUsers?: number;
  activeAdminUsers?: number;

  // Photo Stats
  totalPhotos: number;

  // Recent Activity
  recentTrips: any[];
  recentBlogs: any[];
  recentInquiries: any[];
}

export default function AdminDashboardPage() {
  const { hasPermission, adminUser } = useAuth();
  const toast = useToast();

  // Fetch dashboard data using React Query
  const {
    data: stats,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const response = await fetch('/api/admin/dashboard');
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!hasPermission, // Only run query if hasPermission is available
  });

  const refreshDashboard = async () => {
    try {
      await refetch();
      toast.success('Dashboard refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh dashboard');
    }
  };

  // Show loading skeleton while data is loading
  if (isLoading) {
    return (
      <AdminLayout>
        <DashboardSkeleton />
      </AdminLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <AdminLayout>
        <div className="text-center py-16">
          <div className="bg-red-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
            <Shield className="w-12 h-12 text-red-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Dashboard</h3>
          <p className="text-gray-500 mb-4">There was an error loading your dashboard data.</p>
          <button
            onClick={refreshDashboard}
            className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      </AdminLayout>
    );
  }

  // Default stats if no data
  const defaultStats: DashboardStats = {
    totalTrips: 0,
    activeTrips: 0,
    draftTrips: 0,
    totalBlogs: 0,
    publishedBlogs: 0,
    draftBlogs: 0,
    totalInquiries: 0,
    newInquiries: 0,
    respondedInquiries: 0,
    totalPhotos: 0,
    recentTrips: [],
    recentBlogs: [],
    recentInquiries: []
  };

  const currentStats = stats || defaultStats;

  // Get role-based statistics to display - SIMPLIFIED AND DETAILED PER CATEGORY
  const getRoleBasedStats = () => {
    const statCards = [];

    // TRIPS CATEGORY - Complete trip statistics
    if (hasPermission('trips', 'read')) {
      statCards.push(
        {
          title: "Total Trips",
          value: currentStats.totalTrips,
          icon: <Map className="w-6 h-6 text-blue-600" />,
          color: "border-blue-600",
          bgColor: "bg-blue-100"
        },
        {
          title: "Active Trips",
          value: currentStats.activeTrips,
          icon: <CheckCircle className="w-6 h-6 text-green-600" />,
          color: "border-green-600",
          bgColor: "bg-green-100"
        },
        {
          title: "Draft Trips",
          value: currentStats.draftTrips,
          icon: <Calendar className="w-6 h-6 text-yellow-600" />,
          color: "border-yellow-600",
          bgColor: "bg-yellow-100"
        }
      );
    }

    // BLOGS CATEGORY - Complete blog statistics
    if (hasPermission('blog', 'read')) {
      statCards.push(
        {
          title: "Total Blogs",
          value: currentStats.totalBlogs,
          icon: <FileText className="w-6 h-6 text-purple-600" />,
          color: "border-purple-600",
          bgColor: "bg-purple-100"
        },
        {
          title: "Published Blogs",
          value: currentStats.publishedBlogs,
          icon: <CheckCircle className="w-6 h-6 text-green-600" />,
          color: "border-green-600",
          bgColor: "bg-green-100"
        },
        {
          title: "Draft Blogs",
          value: currentStats.draftBlogs,
          icon: <FileText className="w-6 h-6 text-gray-600" />,
          color: "border-gray-600",
          bgColor: "bg-gray-100"
        }
      );
    }

    // INQUIRIES CATEGORY - Complete inquiry statistics
    if (hasPermission('inquiries', 'read')) {
      statCards.push(
        {
          title: "Total Inquiries",
          value: currentStats.totalInquiries,
          icon: <MessageSquare className="w-6 h-6 text-amber-600" />,
          color: "border-amber-600",
          bgColor: "bg-amber-100"
        },
        {
          title: "New Inquiries",
          value: currentStats.newInquiries,
          icon: <MessageSquare className="w-6 h-6 text-red-600" />,
          color: "border-red-600",
          bgColor: "bg-red-100"
        },
        {
          title: "Responded",
          value: currentStats.respondedInquiries,
          icon: <CheckCircle className="w-6 h-6 text-green-600" />,
          color: "border-green-600",
          bgColor: "bg-green-100"
        }
      );
    }

    // PHOTOS CATEGORY - Only for photo managers and super admins
    if (hasPermission('trip_photos', 'read')) {
      statCards.push({
        title: "Total Photos",
        value: currentStats.totalPhotos,
        icon: <TrendingUp className="w-6 h-6 text-indigo-600" />,
        color: "border-indigo-600",
        bgColor: "bg-indigo-100"
      });
    }

    // ADMIN USERS CATEGORY - Only for super admins
    if (hasPermission('users', 'read') && currentStats.totalAdminUsers !== undefined) {
      statCards.push(
        {
          title: "Total Admin Users",
          value: currentStats.totalAdminUsers,
          icon: <Users className="w-6 h-6 text-gray-600" />,
          color: "border-gray-600",
          bgColor: "bg-gray-100"
        },
        {
          title: "Active Admins",
          value: currentStats.activeAdminUsers || 0,
          icon: <Users className="w-6 h-6 text-green-600" />,
          color: "border-green-600",
          bgColor: "bg-green-100"
        }
      );
    }

    return statCards;
  };

  return (
    <AdminLayout>
      {/* Modern Header with Gradient */}
      <div className="mb-8 relative">
        <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 rounded-2xl p-8 text-white shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-2">
                Welcome back, {adminUser?.full_name || adminUser?.username || 'Admin'}! 👋
              </h1>
              <p className="text-purple-100 text-lg">
                Here's what's happening with your platform today
              </p>
              <button
                onClick={refreshDashboard}
                className="mt-3 px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors duration-200 backdrop-blur-sm border border-white/20 flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                Refresh Data
              </button>
            </div>
            <div className="hidden md:block">
              <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <TrendingUp className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>
          <div className="flex flex-wrap gap-2 mt-4">
            {adminUser?.roles.map(role => (
              <span
                key={role.name}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm"
              >
                {role.name.replace('_', ' ').toUpperCase()}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Modern Statistics Grid */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
          <TrendingUp className="w-6 h-6 text-purple-600" />
          Analytics Overview
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {getRoleBasedStats().map((stat, index) => (
            <div key={index} className="group relative">
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100 hover:border-purple-200 transform hover:-translate-y-1">
                <div className="flex justify-between items-start mb-4">
                  <div className={`p-3 rounded-xl ${stat.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                    {stat.icon}
                  </div>
                  <div className="text-right">
                      <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                  <div className={`h-1 rounded-full ${stat.color.replace('border-', 'bg-')} opacity-20`}></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modern Recent Activity */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
          <Calendar className="w-6 h-6 text-purple-600" />
          Recent Activity
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Trips */}
          {hasPermission('trips', 'read') && currentStats.recentTrips.length > 0 && (
            <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-xl">
                    <Map className="w-5 h-5 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Recent Trips</h3>
                </div>
                <Link href="/admin/trips" className="text-blue-600 hover:text-blue-800 text-sm font-medium hover:underline">
                  View all →
                </Link>
              </div>
              <div className="space-y-4">
                {currentStats.recentTrips.slice(0, 3).map((trip: any) => (
                  <div key={trip.id} className="flex items-center space-x-3 p-3 hover:bg-blue-50 rounded-xl transition-colors duration-200 cursor-pointer">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Map className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{trip.title}</p>
                      <p className="text-xs text-gray-500">{trip.destination}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recent Blogs */}
          {hasPermission('blog', 'read') && currentStats.recentBlogs.length > 0 && (
            <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-xl">
                    <FileText className="w-5 h-5 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Recent Blogs</h3>
                </div>
                <Link href="/admin/blogs" className="text-purple-600 hover:text-purple-800 text-sm font-medium hover:underline">
                  View all →
                </Link>
              </div>
              <div className="space-y-4">
                {currentStats.recentBlogs.slice(0, 3).map((blog: any) => (
                  <div key={blog.id} className="flex items-center space-x-3 p-3 hover:bg-purple-50 rounded-xl transition-colors duration-200 cursor-pointer">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <FileText className="w-5 h-5 text-purple-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{blog.title}</p>
                      <div className="flex items-center gap-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${blog.is_published ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}`}>
                          {blog.is_published ? 'Published' : 'Draft'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recent Inquiries */}
          {hasPermission('inquiries', 'read') && currentStats.recentInquiries.length > 0 && (
            <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-amber-100 rounded-xl">
                    <MessageSquare className="w-5 h-5 text-amber-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Recent Inquiries</h3>
                </div>
                <Link href="/admin/inquiries" className="text-amber-600 hover:text-amber-800 text-sm font-medium hover:underline">
                  View all →
                </Link>
              </div>
              <div className="space-y-4">
                {currentStats.recentInquiries.slice(0, 3).map((inquiry: any) => (
                  <div key={inquiry.id} className="flex items-center space-x-3 p-3 hover:bg-amber-50 rounded-xl transition-colors duration-200 cursor-pointer">
                    <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center">
                      <MessageSquare className="w-5 h-5 text-amber-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{inquiry.name}</p>
                      <p className="text-xs text-gray-500 truncate">{inquiry.email}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modern Empty State */}
      {getRoleBasedStats().length === 0 && (
        <div className="text-center py-16">
          <div className="bg-gradient-to-br from-purple-100 to-blue-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
            <Users className="w-12 h-12 text-purple-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No Data Available</h3>
          <p className="text-gray-500 mb-4">Contact your administrator for access to dashboard features.</p>
          <div className="inline-flex items-center px-4 py-2 bg-purple-100 text-purple-700 rounded-lg text-sm font-medium">
            <Shield className="w-4 h-4 mr-2" />
            Secure Access Required
          </div>
        </div>
      )}
    </AdminLayout>
  );
} 