import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Suspense } from 'react'
import { createServerSupabase } from '@/lib/supabase-server'
import TripsClient from '@/components/trips/TripsClient'
import TripsPageSkeleton from '@/components/trips/TripsPageSkeleton'
import { TripDifficulty } from '@/types/database'
import { JsonLd } from '@/components/seo/JsonLd'

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'Explore Educational Tours & Adventures | Positive7 Tourism',
  description: 'Discover amazing educational tours and adventures across India with Positive7 Tourism. Find your perfect journey with customizable trips for students and groups.',
  keywords: 'educational tours, adventure trips, India travel, student trips, group tours, trekking, Positive7 Tourism',
  openGraph: {
    title: 'Educational Tours & Adventures | Positive7 Tourism',
    description: 'Discover educational tours and adventures across India with Positive7. Perfect for students, schools and groups.',
    type: 'website',
    url: 'https://positive7.org/trips',
    images: [
      {
        url: 'https://positive7.org/images/trips/trips-og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Positive7 Educational Tours'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Educational Tours & Adventures | Positive7 Tourism',
    description: 'Discover amazing educational tours and adventures across India with Positive7 Tourism.',
    images: ['https://res.cloudinary.com/peebst3r/image/upload/v1748754487/positive7/trips/Manali-River.jpg']
  }
}

// Fetch trips from the database
async function getTrips() {
  const supabase = createServerSupabase()
  
  const { data: trips, error } = await supabase
    .from('trips')
    .select('*')
    .eq('is_active', true)
    .order('created_at', { ascending: false })
    
  if (error) {
    console.error('Error fetching trips:', error)
    return []
  }
  
  // Transform the database trip data to match the expected client format
  return trips.map(trip => ({
    id: trip.id,
    title: trip.title,
    slug: trip.slug,
    description: trip.description || '',
    destination: trip.destination,
    days: trip.days || 0,
    nights: trip.nights || 0,
    price_per_person: trip.price_per_person || 0,
    difficulty: trip.difficulty || 'moderate' as TripDifficulty,
    featured_image_url: trip.featured_image_url || '/images/fallback-trip.jpg',
    is_featured: trip.is_featured === true,
    is_active: trip.is_active === true,
    is_trek: trip.is_trek === true
  }))
}

export default async function TripsPage() {
  const trips = await getTrips()

  // Structured data for the trips collection page
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Educational Tours & Adventures",
    "description": "Discover amazing educational tours and adventures across India with Positive7 Tourism.",
    "url": "https://positive7.org/trips",
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": trips.slice(0, 10).map((trip, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "TouristTrip",
          "name": trip.title,
          "description": trip.description,
          "url": `https://positive7.org/trips/${trip.slug}`
        }
      }))
    }
  }

  return (
    <>
      {/* Add JSON-LD structured data */}
      <JsonLd data={structuredData} />
      
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <Suspense fallback={<TripsPageSkeleton />}>
              <TripsClient trips={trips} />
            </Suspense>
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
