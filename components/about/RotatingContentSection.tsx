'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, ArrowRight } from 'lucide-react'
import { ValuesSection } from './ValuesSection'
import { StatsSection } from './StatsSection'
import { TimelineSection } from './TimelineSection'

const SECTIONS = [
  {
    id: 'purpose',
    title: 'Our Purpose',
    component: () => (
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Purpose</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We exist to create transformative learning experiences that go beyond textbooks and classrooms
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            {/* Mission */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-8 h-8 text-white"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="6"/><circle cx="12" cy="12" r="2"/></svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
              <p className="text-gray-700 leading-relaxed">
                To provide safe, educational, and transformative travel experiences that foster personal growth,
                cultural understanding, and lifelong learning. We are committed to creating memories that last a
                lifetime while ensuring the highest standards of safety and educational value.
              </p>
            </div>

            {/* Vision */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-green-700 rounded-full flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-8 h-8 text-white"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
              <p className="text-gray-700 leading-relaxed">
                To be India's most trusted educational tour company, recognized for our innovative approach to
                experiential learning. We envision a world where every student has access to transformative
                travel experiences that shape them into confident, culturally aware global citizens.
              </p>
            </div>
          </div>
        </div>
      </section>
    )
  },
  {
    id: 'values',
    title: 'Core Values',
    component: ValuesSection
  },
  {
    id: 'impact',
    title: 'Impact in Numbers',
    component: StatsSection
  },
  {
    id: 'journey',
    title: 'Our Journey',
    component: TimelineSection
  }
]

export function RotatingContentSection() {
  const [activeIndex, setActiveIndex] = useState(0)

  // Function to go to next section
  const goToNext = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % SECTIONS.length)
  }

  // Function to go to previous section
  const goToPrev = () => {
    setActiveIndex((prevIndex) => (prevIndex - 1 + SECTIONS.length) % SECTIONS.length)
  }

  // Function to go to specific section
  const goToSection = (index: number) => {
    setActiveIndex(index)
  }



  // Auto-rotation removed - only manual navigation now

  // Animation variants
  const variants = {
    enter: { opacity: 0, y: 20 },
    center: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  }

  return (
    <div className="relative">
      {/* Navigation Controls */}
      <div className="flex justify-between items-center max-w-7xl mx-auto px-4 py-4">
        <div className="flex space-x-2">
          {SECTIONS.map((section, index) => (
            <button
              key={section.id}
              onClick={() => goToSection(index)}
              className={`px-4 py-2 rounded-full transition-colors ${
                activeIndex === index
                  ? 'bg-gradient-to-r from-blue-600 to-green-600 text-white font-medium'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
            >
              {section.title}
            </button>
          ))}
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={goToPrev}
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            aria-label="Previous section"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          <button
            onClick={goToNext}
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            aria-label="Next section"
          >
            <ArrowRight className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Content Area */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeIndex}
          initial="enter"
          animate="center"
          exit="exit"
          variants={variants}
          transition={{ duration: 0.5 }}
        >
          {SECTIONS[activeIndex].component()}
        </motion.div>
      </AnimatePresence>
    </div>
  )
} 