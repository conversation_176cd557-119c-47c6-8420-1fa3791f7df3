'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Star,
  Quote,
  ChevronLeft,
  ChevronRight,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Testimonial } from '@/types/database';

interface TestimonialData {
  id: string;
  name: string;
  rating: number;
  title: string | null;
  content: string;
  image_url: string | null;
  is_featured: boolean;
}

interface TestimonialsSectionProps {
  testimonials: TestimonialData[];
}

// Fallback testimonials if none are provided
const fallbackTestimonials: TestimonialData[] = [
  {
    id: 'fallback-1',
    name: 'Student Parent',
    rating: 5,
    title: 'Amazing Experience',
    content: 'Positive7 provided an incredible educational experience for our children. The attention to detail and care for student safety was exceptional.',
    image_url: null,
    is_featured: true,
  },
  {
    id: 'fallback-2',
    name: 'School Coordinator',
    rating: 5,
    title: 'Professional Service',
    content: 'Working with Positive7 has been a pleasure. Their team is professional, organized, and truly cares about creating meaningful educational experiences.',
    image_url: null,
    is_featured: true,
  },
];

export default function TestimonialsSection({ testimonials }: TestimonialsSectionProps) {
  // Use provided testimonials or fallback
  const displayTestimonials = testimonials.length > 0 ? testimonials : fallbackTestimonials;

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, displayTestimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % displayTestimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + displayTestimonials.length) % displayTestimonials.length);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          'h-5 w-5',
          i < rating
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300'
        )}
      />
    ));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section className="section-padding bg-gradient-to-br from-slate-50 via-white to-coral-50/30">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-coral-500 to-orange-500 rounded-2xl mb-6">
            <Quote className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            What Our <span className="bg-gradient-to-r from-coral-600 to-orange-600 bg-clip-text text-transparent">Students Say</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Real experiences from students, parents, and educators who have joined our educational journeys.
            Their stories inspire us to continue creating meaningful travel experiences.
          </p>
        </motion.div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-5xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -20 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className="card-modern p-12 md:p-16 relative overflow-hidden"
            >
              {/* Modern Background Elements */}
              <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-coral-100 to-orange-100 rounded-full -translate-y-20 translate-x-20 opacity-60"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-teal-100 to-coral-100 rounded-full translate-y-16 -translate-x-16 opacity-60"></div>
              <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-coral-400 rounded-full animate-pulse"></div>
              <div className="absolute top-1/4 right-1/4 w-1 h-1 bg-teal-400 rounded-full animate-float"></div>

              <div className="relative z-10 text-center">
                {/* Rating */}
                <div className="flex items-center justify-center space-x-1 mb-8">
                  {renderStars(displayTestimonials[currentIndex].rating)}
                  <span className="ml-3 text-gray-600 font-bold text-lg">
                    {displayTestimonials[currentIndex].rating}.0
                  </span>
                </div>

                {/* Large Quote Icon */}
                <div className="mb-8">
                  <Quote className="h-16 w-16 text-coral-400 mx-auto opacity-60" />
                </div>

                {/* Content */}
                <blockquote className="text-2xl md:text-3xl text-gray-800 leading-relaxed mb-12 font-light max-w-4xl mx-auto">
                  "{displayTestimonials[currentIndex].content}"
                </blockquote>

                {/* Author Info */}
                <div className="flex flex-col items-center space-y-2">
                  <div className="w-16 h-16 bg-gradient-to-r from-coral-500 to-orange-500 rounded-full flex items-center justify-center mb-4">
                    <User className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-gray-900 mb-1">
                      {displayTestimonials[currentIndex].name}
                    </h4>
                    <p className="text-coral-600 font-semibold">
                      {displayTestimonials[currentIndex].title || 'Valued Customer'}
                    </p>
                  </div>
                </div>

              </div>
            </motion.div>
          </AnimatePresence>

          {/* Modern Navigation Controls */}
          <div className="flex items-center justify-center mt-12 space-x-6">
            {/* Previous Button */}
            <button
              onClick={prevTestimonial}
              className="p-4 bg-gradient-to-r from-coral-500 to-orange-500 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-6 w-6" />
            </button>

            {/* Dots Indicator */}
            <div className="flex space-x-3">
              {displayTestimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToTestimonial(index)}
                  className={cn(
                    'w-4 h-4 rounded-full transition-all duration-300 hover:scale-110',
                    currentIndex === index
                      ? 'bg-gradient-to-r from-coral-500 to-orange-500 scale-125 shadow-lg'
                      : 'bg-gray-300 hover:bg-coral-300'
                  )}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>

            {/* Next Button */}
            <button
              onClick={nextTestimonial}
              className="p-4 bg-gradient-to-r from-coral-500 to-orange-500 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-6 w-6" />
            </button>
          </div>
        </div>



        {/* Modern CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-20"
        >
          <div className="card-modern p-12 max-w-2xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Ready to Create Your Own <span className="bg-gradient-to-r from-coral-600 to-orange-600 bg-clip-text text-transparent">Memorable Experience?</span>
            </h3>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Join thousands of students who have discovered the joy of educational travel with us.
            </p>
            <Link href="/trips" className="btn-gradient px-10 py-4 text-lg inline-flex items-center gap-2 rounded-2xl font-bold">
              Start Your Journey
              <ChevronRight className="w-5 h-5" />
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
