'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/lib/hooks/useAuth';
import {
  Home,
  Map,
  FileText,
  MessageSquare,
  Settings,
  LogOut,
  Users,
  BarChart2,
  Image as ImageIcon,
  X,
  Activity
} from 'lucide-react';

interface SidebarLinkProps {
  href: string;
  icon: React.ReactNode;
  text: string;
  isActive: boolean;
  onClose?: () => void;
}

const SidebarLink = ({ href, icon, text, isActive, onClose }: SidebarLinkProps) => (
  <Link 
    href={href as any}
    onClick={onClose}
    className={`
      flex items-center gap-3 px-4 py-3 rounded-lg transition-colors
      ${isActive 
        ? 'bg-blue-100 text-blue-700' 
        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}
    `}
  >
    <div className={isActive ? 'text-blue-700' : 'text-gray-500'}>
      {icon}
    </div>
    <span className="font-medium">{text}</span>
  </Link>
);

interface AdminSidebarProps {
  onClose?: () => void;
}

export default function AdminSidebar({ onClose }: AdminSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { hasPermission, user } = useAuth();

  // Check if user is super admin
  const isSuperAdmin = user?.roles?.some(role => role.name === 'super_admin') || false;
  
  const isActive = (path: string) => {
    if (path === '/admin' && pathname === '/admin') return true;
    if (path !== '/admin' && pathname?.startsWith(path)) return true;
    return false;
  };

  const handleLogout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Redirect to login page
        window.location.href = '/admin/login';
      }
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div className="h-full flex flex-col bg-white border-r border-gray-200">
      <div className="flex items-center justify-between p-4 lg:p-6">
        <Link href="/admin" className="flex items-center">
          <Image 
            src="/images/positive7-logo.png" 
            width={32}
            height={32}
            className="h-8 w-auto mr-2" 
            alt="Positive7 Admin Logo" 
          />
          <h1 className="text-xl lg:text-2xl font-bold text-gray-900">Admin</h1>
        </Link>
        {onClose && (
          <button 
            className="lg:hidden p-1 rounded-full hover:bg-gray-100"
            onClick={onClose}
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        )}
      </div>
      
      <div className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
        {/* Dashboard - Always visible to all admin users */}
        <SidebarLink
          href="/admin"
          icon={<Home className="w-5 h-5" />}
          text="Dashboard"
          isActive={isActive('/admin')}
          onClose={onClose}
        />

        {/* Trips - Only for users with trips read permission */}
        {hasPermission('trips', 'read') && (
          <SidebarLink
            href="/admin/trips"
            icon={<Map className="w-5 h-5" />}
            text="Trips"
            isActive={isActive('/admin/trips')}
            onClose={onClose}
          />
        )}

        {/* Trip Photos - Only for users with trip_photos read permission */}
        {hasPermission('trip_photos', 'read') && (
          <SidebarLink
            href="/admin/trips-photos"
            icon={<ImageIcon className="w-5 h-5" />}
            text="Trip Photos"
            isActive={isActive('/admin/trips-photos')}
            onClose={onClose}
          />
        )}

        {/* Blogs - Only for users with blog read permission */}
        {hasPermission('blog', 'read') && (
          <SidebarLink
            href="/admin/blogs"
            icon={<FileText className="w-5 h-5" />}
            text="Blogs"
            isActive={isActive('/admin/blogs')}
            onClose={onClose}
          />
        )}

        {/* Inquiries - Only for users with inquiries read permission */}
        {hasPermission('inquiries', 'read') && (
          <SidebarLink
            href="/admin/inquiries"
            icon={<MessageSquare className="w-5 h-5" />}
            text="Inquiries"
            isActive={isActive('/admin/inquiries')}
            onClose={onClose}
          />
        )}

        {/* Team Members - Only for users with team_members read permission */}
        {hasPermission('team_members', 'read') && (
          <SidebarLink
            href="/admin/team-members"
            icon={<Users className="w-5 h-5" />}
            text="Team Members"
            isActive={isActive('/admin/team-members')}
            onClose={onClose}
          />
        )}

        {/* Admin Users - Only for users with users read permission (super admin) */}
        {hasPermission('users', 'read') && (
          <SidebarLink
            href="/admin/users"
            icon={<Users className="w-5 h-5" />}
            text="Admin Users"
            isActive={isActive('/admin/users')}
            onClose={onClose}
          />
        )}

        {/* Dev Status - Only for super admins */}
        {isSuperAdmin && (
          <SidebarLink
            href="/admin/dev-status"
            icon={<Activity className="w-5 h-5" />}
            text="Dev Status"
            isActive={isActive('/admin/dev-status')}
            onClose={onClose}
          />
        )}
      </div>
      
      <div className="p-4 border-t border-gray-200">
        <button 
          onClick={handleLogout}
          className="w-full flex items-center gap-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
        >
          <LogOut className="w-5 h-5" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
} 