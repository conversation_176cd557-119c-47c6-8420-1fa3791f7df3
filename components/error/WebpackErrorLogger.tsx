'use client';

import { useEffect } from 'react';

export function WebpackErrorLogger() {
  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // Capture webpack errors
    const originalError = console.error;
    console.error = (...args) => {
      // Call original console.error
      originalError.apply(console, args);
      
      // Check if this looks like a webpack error
      const errorString = args.join(' ');
      if (errorString.includes('webpack') || 
          errorString.includes('Module not found') ||
          errorString.includes('Failed to compile') ||
          errorString.includes('SyntaxError') ||
          errorString.includes('TypeError') ||
          errorString.includes('ReferenceError')) {
        
        // Log to terminal via API
        fetch('/api/dev/log-error', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'WEBPACK_ERROR',
            message: errorString,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            errorId: Math.random().toString(36).substring(2, 15),
          }),
        }).catch(err => {
          originalError('Failed to log webpack error to terminal:', err);
        });
      }
    };

    // Capture unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      
      fetch('/api/dev/log-error', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'UNHANDLED_PROMISE_REJECTION',
          message: errorMessage,
          stack: errorStack,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          errorId: Math.random().toString(36).substring(2, 15),
        }),
      }).catch(err => {
        console.error('Failed to log promise rejection to terminal:', err);
      });
    };

    // Capture global errors
    const handleGlobalError = (event: ErrorEvent) => {
      fetch('/api/dev/log-error', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'GLOBAL_ERROR',
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          errorId: Math.random().toString(36).substring(2, 15),
        }),
      }).catch(err => {
        console.error('Failed to log global error to terminal:', err);
      });
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleGlobalError);

    // Cleanup
    return () => {
      console.error = originalError;
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleGlobalError);
    };
  }, []);

  return null;
}
