'use client';

import { useEffect, useState } from 'react';
import { useHasMounted } from '@/components/common/ClientOnly';

export function ErrorMonitor() {
  const hasMounted = useHasMounted();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Only run in development and after mounting
    if (process.env.NODE_ENV !== 'development' || !hasMounted || isInitialized) {
      return;
    }

    // Delay initialization to prevent hydration issues
    const initTimer = setTimeout(() => {
      try {
        console.log('🔍 Error Monitor initialized');
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize Error Monitor:', error);
      }
    }, 1000);

    return () => clearTimeout(initTimer);
  }, [hasMounted, isInitialized]);

  useEffect(() => {
    // Only run monitoring after initialization
    if (!isInitialized || process.env.NODE_ENV !== 'development') {
      return;
    }

    // Monitor for font loading errors
    const checkFontLoading = () => {
      if (document.fonts) {
        document.fonts.ready.then(() => {
          console.log('✅ All fonts loaded successfully');
        }).catch((error) => {
          console.error('❌ Font loading error:', error);
          fetch('/api/dev/log-error', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              type: 'FONT_LOADING_ERROR',
              message: error.message || 'Font loading failed',
              timestamp: new Date().toISOString(),
              url: window.location.href,
              errorId: Math.random().toString(36).substring(2, 15),
            }),
          }).catch(console.error);
        });

        // Check for specific font failures
        const fonts = ['Inter', 'Poppins', 'Montserrat'];
        fonts.forEach(fontFamily => {
          if (!document.fonts.check(`16px ${fontFamily}`)) {
            console.warn(`⚠️ Font ${fontFamily} may not be loaded`);
          }
        });
      }
    };

    // Monitor for network errors
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        
        // Log 404 errors for static assets
        if (!response.ok && response.status === 404) {
          const url = args[0] as string;
          if (url.includes('_next/static') || url.includes('.woff') || url.includes('.css') || url.includes('.js')) {
            console.error(`❌ 404 Error for static asset: ${url}`);
            fetch('/api/dev/log-error', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                type: 'STATIC_ASSET_404',
                message: `404 error for: ${url}`,
                url: window.location.href,
                assetUrl: url,
                timestamp: new Date().toISOString(),
                errorId: Math.random().toString(36).substring(2, 15),
              }),
            }).catch(console.error);
          }
        }
        
        return response;
      } catch (error) {
        console.error('❌ Fetch error:', error);
        return originalFetch(...args);
      }
    };

    // Monitor for React hydration errors
    const checkHydrationErrors = () => {
      const hydrationErrors = document.querySelectorAll('[data-reactroot]');
      if (hydrationErrors.length === 0) {
        // Check for hydration mismatches
        setTimeout(() => {
          const textNodes = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
          );
          
          let node;
          while (node = textNodes.nextNode()) {
            if (node.textContent?.includes('Hydration failed') || 
                node.textContent?.includes('Text content does not match')) {
              console.error('❌ Hydration error detected:', node.textContent);
              fetch('/api/dev/log-error', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  type: 'HYDRATION_ERROR',
                  message: node.textContent,
                  timestamp: new Date().toISOString(),
                  url: window.location.href,
                  errorId: Math.random().toString(36).substring(2, 15),
                }),
              }).catch(console.error);
            }
          }
        }, 1000);
      }
    };

    // Monitor for console errors
    const originalConsoleError = console.error;
    console.error = (...args) => {
      originalConsoleError.apply(console, args);
      
      const errorMessage = args.join(' ');
      
      // Filter out known non-critical errors
      const ignoredErrors = [
        'Warning: validateDOMNesting',
        'Warning: Each child in a list should have a unique "key" prop',
        'Failed to log error to terminal', // Prevent infinite loops
      ];
      
      if (!ignoredErrors.some(ignored => errorMessage.includes(ignored))) {
        fetch('/api/dev/log-error', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'CONSOLE_ERROR',
            message: errorMessage,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            errorId: Math.random().toString(36).substring(2, 15),
          }),
        }).catch(() => {}); // Silent fail to prevent loops
      }
    };

    // Run checks
    setTimeout(checkFontLoading, 1000);
    setTimeout(checkHydrationErrors, 2000);

    // Cleanup
    return () => {
      try {
        if (typeof window !== 'undefined') {
          window.fetch = originalFetch;
          console.error = originalConsoleError;
        }
      } catch (error) {
        console.error('Error during ErrorMonitor cleanup:', error);
      }
    };
  }, [isInitialized]);

  return null;
}
