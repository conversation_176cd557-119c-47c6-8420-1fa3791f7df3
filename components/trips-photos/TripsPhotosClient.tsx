'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Download,
  Calendar,
  MapPin,
  Users,
  Camera,
  Search,
  ExternalLink,
  AlertCircle,
  Lock,
  Unlock
} from 'lucide-react'

interface TripPhotoAlbum {
  id: string
  title: string
  date: string
  coverImage: string
  downloadLink: string
  description: string
  password?: string | null // 'protected' if password protected, null if public
}

interface TripsPhotosClientProps {
  albums: TripPhotoAlbum[]
}

export default function TripsPhotosClient({ albums }: TripsPhotosClientProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false)
  const [selectedAlbum, setSelectedAlbum] = useState<TripPhotoAlbum | null>(null)
  const [password, setPassword] = useState('')
  const [passwordError, setPasswordError] = useState(false)
  const [unlockSuccess, setUnlockSuccess] = useState(false)
  // Store unlocked album IDs to persist through the session
  const [unlockedAlbums, setUnlockedAlbums] = useState<Set<string>>(new Set())

  // Filter albums
  const filteredAlbums = albums.filter(album => {
    const matchesSearch = !searchQuery ||
      album.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      album.description.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesSearch
  })

  // Handle album click for password-protected albums
  const handleAlbumClick = (album: TripPhotoAlbum) => {
    // If album has a password and hasn't been unlocked yet
    if (album.password && !unlockedAlbums.has(album.id)) {
      setSelectedAlbum(album)
      setIsPasswordModalOpen(true)
      setPassword('')
      setPasswordError(false)
      setUnlockSuccess(false)
    } else {
      // If album is not password protected or already unlocked, open the download link
      if (album.downloadLink && album.downloadLink !== '#') {
        window.open(album.downloadLink, '_blank')
      }
    }
  }

  // Handle password submission with secure API call
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedAlbum) return

    try {
      const response = await fetch('/api/trips-photos/verify-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          albumId: selectedAlbum.id,
          password: password
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setPasswordError(false)
        setUnlockSuccess(true)

        // Add to unlocked albums
        const newUnlockedAlbums = new Set(unlockedAlbums)
        newUnlockedAlbums.add(selectedAlbum.id)
        setUnlockedAlbums(newUnlockedAlbums)

        // Close modal after a short delay to show success message
        setTimeout(() => {
          setIsPasswordModalOpen(false)
          // Open the download link after closing the modal
          if (data.downloadLink && data.downloadLink !== '#') {
            window.open(data.downloadLink, '_blank')
          }
        }, 1500)
      } else {
        setPasswordError(true)
        setUnlockSuccess(false)
      }
    } catch (error) {
      console.error('Password verification failed:', error)
      setPasswordError(true)
      setUnlockSuccess(false)
    }
  }

  // Close the password modal
  const closePasswordModal = () => {
    setIsPasswordModalOpen(false)
    setSelectedAlbum(null)
    setPassword('')
    setPasswordError(false)
    setUnlockSuccess(false)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <motion.div
          className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <Camera className="w-8 h-8 text-blue-600" />
        </motion.div>
        
        <motion.h1 
          className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          Trips Photos
        </motion.h1>
        
        <motion.div
          className="max-w-3xl mx-auto space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <p className="text-xl text-gray-600">
            "I don't trust words. I trust pictures." – Gilles Peress
          </p>
          <p className="text-gray-600">
            All the TRIPS PHOTOS of all trips will be uploaded here. You can download photos and see where the participants are enjoying their time.
          </p>
        </motion.div>

        {/* Important Notice */}
        <motion.div
          className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="flex items-center gap-2 text-amber-800">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">Important Note:</span>
          </div>
          <p className="text-amber-700 mt-1">
            After the end of the trip, all the Trips Photos will be removed after 14 days.
          </p>
        </motion.div>
      </div>

      {/* Search and Filters */}
      <motion.div
        className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search trips..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 sm:py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base"
            />
          </div>

          {/* Search Info */}
          <div className="flex items-center justify-center sm:justify-start gap-4">
            <span className="text-sm text-gray-500 text-center sm:text-left">
              {filteredAlbums.length} {filteredAlbums.length === 1 ? 'album' : 'albums'} found
            </span>
          </div>
        </div>


      </motion.div>

      {/* Photo Albums Grid */}
      {filteredAlbums.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
          {filteredAlbums.map((album, index) => (
            <TripPhotoCard
              key={album.id}
              album={album}
              index={index}
              isUnlocked={!album.password || unlockedAlbums.has(album.id)}
              onClick={() => handleAlbumClick(album)}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Camera className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">No photo albums found</h3>
          <p className="text-gray-600">
            Try adjusting your search criteria or filters
          </p>
        </div>
      )}

      {/* Modern Password Modal */}
      <AnimatePresence>
        {isPasswordModalOpen && selectedAlbum && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={closePasswordModal}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: "spring", duration: 0.5 }}
              className="bg-white rounded-3xl max-w-md w-full shadow-2xl overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header with gradient */}
              <div className="bg-gradient-to-r from-coral-500 to-orange-500 p-8 text-white text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                  className="inline-flex items-center justify-center w-16 h-16 bg-white/20 backdrop-blur-md rounded-full mb-4"
                >
                  <Lock className="w-8 h-8" />
                </motion.div>
                <h3 className="text-2xl font-bold mb-2">Protected Album</h3>
                <p className="text-white/90">
                  Enter the password to access "{selectedAlbum.title}"
                </p>
              </div>

              {/* Form */}
              <div className="p-8">
                <form onSubmit={handlePasswordSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-3">
                      Album Password
                    </label>
                    <div className="relative">
                      <input
                        type="password"
                        id="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className={`w-full px-4 py-4 text-base border-2 rounded-2xl focus:ring-0 transition-all duration-300 ${
                          passwordError
                            ? 'border-red-300 focus:border-red-500 bg-red-50'
                            : unlockSuccess
                            ? 'border-green-300 focus:border-green-500 bg-green-50'
                            : 'border-gray-200 focus:border-coral-500 bg-gray-50'
                        }`}
                        placeholder="Enter password..."
                        autoFocus
                        autoComplete="off"
                      />
                      {unlockSuccess && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2"
                        >
                          <Unlock className="w-5 h-5 text-green-500" />
                        </motion.div>
                      )}
                    </div>

                    <AnimatePresence>
                      {passwordError && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl"
                        >
                          <p className="text-sm text-red-700 flex items-center">
                            <AlertCircle className="w-4 h-4 mr-2" />
                            Incorrect password. Please try again.
                          </p>
                        </motion.div>
                      )}

                      {unlockSuccess && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="mt-3 p-3 bg-green-50 border border-green-200 rounded-xl"
                        >
                          <p className="text-sm text-green-700 flex items-center">
                            <Unlock className="w-4 h-4 mr-2" />
                            Password correct! Opening album...
                          </p>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={closePasswordModal}
                      className="flex-1 px-6 py-4 border-2 border-gray-200 text-gray-700 rounded-2xl hover:bg-gray-50 transition-all duration-300 font-semibold"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={unlockSuccess}
                      className={`flex-1 px-6 py-4 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                        unlockSuccess
                          ? 'bg-green-500 text-white cursor-not-allowed'
                          : 'bg-gradient-to-r from-coral-500 to-orange-500 hover:from-coral-600 hover:to-orange-600 text-white shadow-lg hover:shadow-xl'
                      }`}
                    >
                      {unlockSuccess ? (
                        <span className="flex items-center justify-center">
                          <Unlock className="w-4 h-4 mr-2" />
                          Unlocked!
                        </span>
                      ) : (
                        'Unlock Album'
                      )}
                    </button>
                  </div>
                </form>

                {/* Security Notice */}
                <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-xl">
                  <p className="text-xs text-amber-700 text-center">
                    🔒 This album is password protected for privacy. Passwords are verified securely.
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Trip Photo Card Component
function TripPhotoCard({
  album,
  index,
  isUnlocked,
  onClick
}: {
  album: TripPhotoAlbum;
  index: number;
  isUnlocked: boolean;
  onClick: () => void;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: '-10%' }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="group"
    >
      <div className="card-modern overflow-hidden hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 hover:rotate-1">
        {/* Album Cover Image */}
        <div className="relative h-72 overflow-hidden">
          <Image
            src={album.coverImage}
            alt={album.title}
            width={400}
            height={300}
            className="w-full h-full object-cover transition-transform duration-1000 group-hover:scale-125"
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/images/placeholder.jpg';
            }}
          />

          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-coral-500/20 via-transparent to-teal-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

          {/* Password Badge */}
          {album.password && (
            <div className="absolute top-6 left-6">
              <span className={`px-4 py-2 text-sm font-bold rounded-2xl backdrop-blur-md border border-white/20 shadow-lg flex items-center gap-2 ${
                isUnlocked ? 'bg-green-500/90 text-white' : 'bg-yellow-500/90 text-white'
              }`}>
                {isUnlocked ? <Unlock className="w-4 h-4" /> : <Lock className="w-4 h-4" />}
                {isUnlocked ? 'Unlocked' : 'Protected'}
              </span>
            </div>
          )}

          {/* Floating Elements */}
          <div className="absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-all duration-500 delay-200">
            <div className="w-3 h-3 bg-secondary-400 rounded-full animate-float" />
          </div>

          {/* Quick Action Overlay */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{
              opacity: 1,
              y: 0
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="absolute bottom-6 left-6 right-6"
          >
            <div className="backdrop-glass rounded-2xl p-4 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-secondary-300" />
                  <span className="font-semibold">{album.date}</span>
                </div>
                <div className="w-2 h-2 bg-coral-400 rounded-full animate-pulse" />
              </div>
            </div>
          </motion.div>
        </div>

        {/* Content */}
        <div className="p-8">
          <div className="mb-4">
            <h3 className="text-2xl font-bold text-gray-900 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-coral-600 group-hover:to-teal-600 group-hover:bg-clip-text transition-all duration-500 leading-tight mb-2">
              {album.title}
            </h3>
            <div className="w-12 h-1 bg-gradient-to-r from-coral-400 to-teal-400 rounded-full group-hover:w-20 transition-all duration-500" />
          </div>

          {album.description && (
            <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed text-base">
              {album.description}
            </p>
          )}

          {/* Album details */}
          <div className="flex items-center justify-between mb-6 text-sm text-gray-500">
            <div className="flex items-center space-x-2 bg-gray-50 rounded-xl px-3 py-2">
              <Camera className="h-4 w-4 text-coral-500" />
              <span className="font-medium">Photo Album</span>
            </div>

            {album.password && (
              <div className={`flex items-center space-x-1 rounded-xl px-3 py-2 ${
                isUnlocked ? 'bg-green-50 text-green-600' : 'bg-yellow-50 text-yellow-600'
              }`}>
                {isUnlocked ? <Unlock className="w-4 h-4" /> : <Lock className="w-4 h-4" />}
                <span className="font-medium">{isUnlocked ? 'Unlocked' : 'Protected'}</span>
              </div>
            )}
          </div>

          {/* Action Button */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4 text-coral-500" />
                <span>Created {album.date}</span>
              </div>
            </div>

            <button
              onClick={onClick}
              className={`
                flex items-center gap-2 px-6 py-3 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105
                ${album.password && !isUnlocked
                  ? 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white hover:from-yellow-500 hover:to-orange-500 shadow-lg hover:shadow-xl'
                  : 'bg-gradient-to-r from-coral-500 to-teal-500 text-white hover:from-coral-600 hover:to-teal-600 shadow-lg hover:shadow-xl'
                }
              `}
            >
              {album.password && !isUnlocked ? (
                <>
                  <Lock className="w-4 h-4" />
                  Unlock
                </>
              ) : (
                <>
                  <Download className="w-4 h-4" />
                  Download
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
