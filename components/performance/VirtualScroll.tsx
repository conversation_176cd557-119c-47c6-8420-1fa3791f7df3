'use client';

import React, { memo } from 'react';
import { FixedSizeList as List } from 'react-window';

interface VirtualScrollProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  className?: string;
  overscanCount?: number;
}

function VirtualScrollComponent<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className = '',
  overscanCount = 5,
}: VirtualScrollProps<T>) {
  const Row = memo(({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      {renderItem(items[index], index)}
    </div>
  ));

  Row.displayName = 'VirtualScrollRow';

  if (items.length === 0) {
    return (
      <div 
        className={`flex items-center justify-center text-gray-500 ${className}`}
        style={{ height: containerHeight }}
      >
        No items to display
      </div>
    );
  }

  return (
    <div className={className}>
      <List
        height={containerHeight}
        itemCount={items.length}
        itemSize={itemHeight}
        overscanCount={overscanCount}
        width="100%"
      >
        {Row}
      </List>
    </div>
  );
}

// Export with proper generic typing
export const VirtualScroll = memo(VirtualScrollComponent) as typeof VirtualScrollComponent;
