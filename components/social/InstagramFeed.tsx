'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Instagram, ExternalLink, Heart, MessageCircle, Share } from 'lucide-react';

interface InstagramPost {
  id: string;
  caption: string;
  media_url: string;
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
  permalink: string;
  timestamp: string;
  like_count?: number;
  comments_count?: number;
}

interface InstagramFeedProps {
  className?: string;
  maxPosts?: number;
  showHeader?: boolean;
  compact?: boolean;
}

export default function InstagramFeed({ 
  className = '', 
  maxPosts = 6, 
  showHeader = true,
  compact = false 
}: InstagramFeedProps) {
  const [posts, setPosts] = useState<InstagramPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchInstagramPosts() {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/instagram');

        if (!response.ok) {
          // If Instagram API is not configured, silently use fallback posts
          console.warn('Instagram API not configured, using fallback posts');
          setPosts(getFallbackPosts().slice(0, maxPosts));
          return;
        }

        const data = await response.json();
        setPosts(data.slice(0, maxPosts));
      } catch (err) {
        console.warn('Instagram API error, using fallback posts:', err);
        // Silently use fallback posts instead of showing error
        setPosts(getFallbackPosts().slice(0, maxPosts));
      } finally {
        setLoading(false);
      }
    }

    fetchInstagramPosts();
  }, [maxPosts]);

  // Fallback posts for when Instagram API is not available
  const getFallbackPosts = (): InstagramPost[] => [
    {
      id: '1',
      caption: '🏔️ Amazing adventure in the Himalayas with our students! #EducationalTour #Adventure #Positive7',
      media_url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date().toISOString(),
      like_count: 127,
      comments_count: 23
    },
    {
      id: '2',
      caption: '🎒 Students exploring the rich heritage of Rajasthan! Learning beyond classrooms. #Heritage #Learning',
      media_url: 'https://images.unsplash.com/photo-1524492412937-b28074a5d7da?w=400&h=400&fit=crop',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 86400000).toISOString(),
      like_count: 89,
      comments_count: 15
    },
    {
      id: '3',
      caption: '🌊 Beach cleanup drive combined with marine biology learning! #Environment #Education #SocialImpact',
      media_url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=400&fit=crop',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 172800000).toISOString(),
      like_count: 156,
      comments_count: 31
    },
    {
      id: '4',
      caption: '🏛️ Exploring ancient architecture and history! Our students are amazed by the craftsmanship. #History #Architecture',
      media_url: 'https://images.unsplash.com/photo-1564507592333-c60657eea523?w=400&h=400&fit=crop',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 259200000).toISOString(),
      like_count: 203,
      comments_count: 42
    },
    {
      id: '5',
      caption: '🌿 Nature walk and wildlife photography session! Connecting with nature through education. #Wildlife #Photography',
      media_url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 345600000).toISOString(),
      like_count: 174,
      comments_count: 28
    },
    {
      id: '6',
      caption: '🎨 Art and culture workshop with local artisans! Hands-on learning at its best. #Art #Culture #Workshop',
      media_url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=400&fit=crop',
      media_type: 'IMAGE',
      permalink: 'https://instagram.com/positive.seven',
      timestamp: new Date(Date.now() - 432000000).toISOString(),
      like_count: 145,
      comments_count: 19
    }
  ];

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - postTime.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const truncateCaption = (caption: string, maxLength: number = 100) => {
    if (caption.length <= maxLength) return caption;
    return caption.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className={`${className}`}>
        {showHeader && (
          <div className="flex items-center gap-3 mb-6">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg animate-pulse"></div>
            <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
          </div>
        )}
        <div className={`grid ${compact ? 'grid-cols-3' : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'} gap-4`}>
          {Array.from({ length: maxPosts }).map((_, index) => (
            <div key={index} className="bg-gray-200 aspect-square rounded-xl animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error && posts.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <Instagram className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">{error}</p>
      </div>
    );
  }

  return (
    <div className={className}>
      {showHeader && (
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Instagram className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">Follow Our Journey</h3>
              <p className="text-gray-600">@positive.seven</p>
            </div>
          </div>
          <a
            href="https://instagram.com/positive.seven"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 text-sm font-medium"
          >
            Follow Us
            <ExternalLink className="w-4 h-4" />
          </a>
        </div>
      )}

      <div className={`grid gap-4 ${
        compact 
          ? 'grid-cols-3' 
          : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
      }`}>
        {posts.map((post, index) => (
          <motion.div
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="group"
          >
            <a
              href={post.permalink}
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <div className="relative aspect-square rounded-xl overflow-hidden bg-gray-100">
                <Image
                  src={post.media_url}
                  alt={post.caption}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300">
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-white text-center p-4">
                      {!compact && (
                        <>
                          <div className="flex items-center justify-center gap-4 mb-2">
                            {post.like_count && (
                              <div className="flex items-center gap-1">
                                <Heart className="w-4 h-4" />
                                <span className="text-sm">{post.like_count}</span>
                              </div>
                            )}
                            {post.comments_count && (
                              <div className="flex items-center gap-1">
                                <MessageCircle className="w-4 h-4" />
                                <span className="text-sm">{post.comments_count}</span>
                              </div>
                            )}
                          </div>
                          <p className="text-sm">{truncateCaption(post.caption, 80)}</p>
                        </>
                      )}
                      <div className="mt-2">
                        <ExternalLink className="w-5 h-5 mx-auto" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Media type indicator */}
                {post.media_type === 'CAROUSEL_ALBUM' && (
                  <div className="absolute top-2 right-2 bg-black/50 rounded-full p-1">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </div>

              {!compact && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {truncateCaption(post.caption)}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    {formatTimeAgo(post.timestamp)}
                  </p>
                </div>
              )}
            </a>
          </motion.div>
        ))}
      </div>

      {!compact && (
        <div className="text-center mt-8">
          <a
            href="https://instagram.com/positive.seven"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 font-medium"
          >
            View More on Instagram
            <Instagram className="w-5 h-5" />
          </a>
        </div>
      )}
    </div>
  );
}
