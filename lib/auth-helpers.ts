import { NextRequest } from 'next/server';
import { createServerSupabaseAuth, getAdminUserWithRoles, hasPermission, AdminUserWithRoles } from '@/lib/auth-server';

interface AuthResult {
  success: boolean;
  adminUser?: AdminUserWithRoles;
  hasPermission?: (resource: string, action: string) => boolean;
  error?: string;
  status?: number;
}

export async function verifyAdminAuth(request: NextRequest): Promise<AuthResult> {
  try {
    const supabase = await createServerSupabaseAuth();

    // Get the current user from the session
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        success: false,
        error: 'Authentication required',
        status: 401
      };
    }

    // Get admin user with roles
    const adminUser = await getAdminUserWithRoles(user.id);

    if (!adminUser || !adminUser.is_active) {
      return {
        success: false,
        error: 'Admin user not found or inactive',
        status: 403
      };
    }

    // Check if user has any roles
    if (!adminUser.roles || adminUser.roles.length === 0) {
      return {
        success: false,
        error: 'No admin roles assigned',
        status: 403
      };
    }

    // Create hasPermission function
    const hasPermissionFn = (resource: string, action: string): boolean => {
      return hasPermission(adminUser.roles, resource, action);
    };

    return {
      success: true,
      adminUser,
      hasPermission: hasPermissionFn
    };

  } catch (error) {
    console.error('Auth verification error:', error);
    return {
      success: false,
      error: 'Internal server error',
      status: 500
    };
  }
}
