import { NextRequest } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

interface AuthResult {
  success: boolean;
  adminUser?: any;
  hasPermission?: (resource: string, action: string) => boolean;
  error?: string;
  status?: number;
}

export async function verifyAdminAuth(request: NextRequest): Promise<AuthResult> {
  try {
    const supabase = createServerSupabase();
    
    // Get the session from the request
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return {
        success: false,
        error: 'Authentication required',
        status: 401
      };
    }

    // Get admin user data
    const { data: adminUser, error: userError } = await supabase
      .from('admin_users')
      .select(`
        *,
        admin_user_roles (
          admin_roles (
            name,
            permissions
          )
        )
      `)
      .eq('id', session.user.id)
      .eq('is_active', true)
      .single();

    if (userError || !adminUser) {
      return {
        success: false,
        error: 'Admin user not found or inactive',
        status: 403
      };
    }

    // Create hasPermission function
    const hasPermission = (resource: string, action: string): boolean => {
      if (!adminUser.admin_user_roles || !Array.isArray(adminUser.admin_user_roles)) return false;

      return adminUser.admin_user_roles.some((userRole: any) => {
        const role = userRole.admin_roles;
        if (!role || !role.permissions) return false;

        const permissions = role.permissions;
        return permissions[resource] && permissions[resource].includes(action);
      });
    };

    // Add roles to admin user for easier access
    const adminUserWithRoles = {
      ...adminUser,
      roles: Array.isArray(adminUser.admin_user_roles)
        ? adminUser.admin_user_roles.map((userRole: any) => userRole.admin_roles)
        : []
    };

    return {
      success: true,
      adminUser: adminUserWithRoles,
      hasPermission
    };

  } catch (error) {
    console.error('Auth verification error:', error);
    return {
      success: false,
      error: 'Internal server error',
      status: 500
    };
  }
}
