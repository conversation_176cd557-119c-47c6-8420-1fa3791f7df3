import { google } from 'googleapis';
import fs from 'fs';
import path from 'path';
import { Readable } from 'stream';

/**
 * Extract folder ID from a Google Drive URL
 * @param url Google Drive folder URL
 * @returns The folder ID or null if not found
 */
export const extractFolderIdFromUrl = (url: string): string | null => {
  if (!url) return null;
  
  // Handle different URL formats
  // Format: https://drive.google.com/drive/folders/FOLDER_ID
  const folderMatch = url.match(/\/folders\/([^/?]+)/);
  if (folderMatch && folderMatch[1]) {
    return folderMatch[1];
  }
  
  // Format: https://drive.google.com/open?id=FOLDER_ID
  const idMatch = url.match(/[?&]id=([^&]+)/);
  if (idMatch && idMatch[1]) {
    return idMatch[1];
  }
  
  return null;
};

/**
 * Extract album ID from a Google Photos URL
 * @param url Google Photos album URL
 * @returns The album ID or null if not found
 */
export const extractAlbumIdFromUrl = (url: string): string | null => {
  if (!url) return null;
  
  // Handle different URL formats
  // Format: https://photos.google.com/albums/ALBUM_ID
  const albumMatch = url.match(/\/albums\/([^/?]+)/);
  if (albumMatch && albumMatch[1]) {
    return albumMatch[1];
  }
  
  // Format: https://photos.app.goo.gl/SHORTENED_ID
  if (url.includes('photos.app.goo.gl/')) {
    // For shortened URLs, we'll just return the full URL as we can't extract the ID reliably
    return url;
  }
  
  return null;
};

/**
 * Check if a URL is a Google Photos link
 * @param url URL to check
 * @returns boolean indicating if it's a Google Photos link
 */
export const isGooglePhotosLink = (url: string | null): boolean => {
  if (!url) return false;
  return url.includes('photos.google.com') || url.includes('photos.app.goo.gl');
};

/**
 * Check if a URL is a Google Drive link
 * @param url URL to check
 * @returns boolean indicating if it's a Google Drive link
 */
export const isGoogleDriveLink = (url: string | null): boolean => {
  if (!url) return false;
  return url.includes('drive.google.com');
};

// Configure Google Drive client with service account credentials
const configureGoogleDrive = () => {
  console.log('[DRIVE] Configuring Google Drive client');

  const credentials = {
    client_email: process.env.GOOGLE_CLIENT_EMAIL,
    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  };

  if (!credentials.client_email || !credentials.private_key) {
    console.error('[DRIVE] Missing Google Drive credentials in environment variables');
    throw new Error('Missing Google Drive credentials');
  }

  console.log(`[DRIVE] Using service account email: ${credentials.client_email}`);

  try {
    const auth = new google.auth.JWT({
      email: credentials.client_email,
      key: credentials.private_key,
      scopes: [
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/photoslibrary', // For Google Photos
      ],
    });

    console.log('[DRIVE] Google client configured successfully');
    return {
      drive: google.drive({ version: 'v3', auth }),
      auth
    };
  } catch (error) {
    console.error('[DRIVE] Error configuring Google client:', error);
    throw new Error(`Google Drive configuration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Upload a file to an existing Google Drive folder using the folder ID
 */
export const uploadToGoogleDrive = async (
  filePath: string,
  fileName: string,
  mimeType: string,
  folderId: string
): Promise<string> => {
  try {
    console.log(`[DRIVE] Starting upload process for file: ${fileName}`);
    console.log(`[DRIVE] File path: ${filePath}`);
    console.log(`[DRIVE] MIME type: ${mimeType}`);
    console.log(`[DRIVE] Target folder ID: ${folderId}`);
    
    if (!folderId) {
      console.error(`[DRIVE] No folder ID provided`);
      throw new Error(`No folder ID provided for upload`);
    }
    
    const { drive } = configureGoogleDrive();
    const fileMetadata = {
      name: fileName,
      parents: [folderId],
    };

    if (!fs.existsSync(filePath)) {
      console.error(`[DRIVE] File not found at path: ${filePath}`);
      throw new Error(`File not found at path: ${filePath}`);
    }

    console.log(`[DRIVE] Creating file stream from: ${filePath}`);
    const media = {
      mimeType,
      body: fs.createReadStream(filePath),
    };

    console.log(`[DRIVE] Uploading file to Google Drive folder with ID: ${folderId}`);
    const response = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id, webViewLink',
    });

    console.log(`[DRIVE] File uploaded successfully with ID: ${response.data.id}`);

    // Make the file publicly accessible
    console.log('[DRIVE] Setting public permissions...');
    await drive.permissions.create({
      fileId: response.data.id as string,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });

    // Get the webViewLink (public URL) for the file
    console.log('[DRIVE] Retrieving public URL...');
    const file = await drive.files.get({
      fileId: response.data.id as string,
      fields: 'webViewLink',
    });

    console.log(`[DRIVE] Public URL generated: ${file.data.webViewLink}`);
    return file.data.webViewLink as string;
  } catch (error) {
    console.error('[DRIVE] Error uploading to Google Drive:', error);
    throw new Error(`Failed to upload file to Google Drive: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Upload a buffer directly to Google Drive without using the filesystem
 * This is more suitable for serverless environments like Vercel
 */
export const uploadBufferToGoogleDrive = async (
  buffer: Buffer,
  fileName: string,
  mimeType: string,
  folderId: string
): Promise<string> => {
  try {
    console.log(`[DRIVE] Starting buffer upload process for file: ${fileName}`);
    console.log(`[DRIVE] Buffer size: ${buffer.length} bytes`);
    console.log(`[DRIVE] MIME type: ${mimeType}`);
    console.log(`[DRIVE] Target folder ID: ${folderId}`);
    
    if (!folderId) {
      console.error(`[DRIVE] No folder ID provided`);
      throw new Error(`No folder ID provided for upload`);
    }
    
    const { drive } = configureGoogleDrive();
    const fileMetadata = {
      name: fileName,
      parents: [folderId],
    };

    // Create a readable stream from the buffer
    const bufferStream = new Readable();
    bufferStream.push(buffer);
    bufferStream.push(null); // Mark the end of the stream

    const media = {
      mimeType,
      body: bufferStream,
    };

    console.log(`[DRIVE] Uploading buffer to Google Drive folder with ID: ${folderId}`);
    const response = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id, webViewLink',
    });

    console.log(`[DRIVE] Buffer uploaded successfully with ID: ${response.data.id}`);

    // Make the file publicly accessible
    console.log('[DRIVE] Setting public permissions...');
    await drive.permissions.create({
      fileId: response.data.id as string,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });

    // Get the webViewLink (public URL) for the file
    console.log('[DRIVE] Retrieving public URL...');
    const file = await drive.files.get({
      fileId: response.data.id as string,
      fields: 'webViewLink',
    });

    console.log(`[DRIVE] Public URL generated: ${file.data.webViewLink}`);
    return file.data.webViewLink as string;
  } catch (error) {
    console.error('[DRIVE] Error uploading buffer to Google Drive:', error);
    throw new Error(`Failed to upload buffer to Google Drive: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Upload a file to Google Photos
 * Note: Currently a placeholder as Google Photos API requires OAuth2 user consent
 * and cannot be fully implemented with a service account
 */
export const uploadToGooglePhotos = async (
  filePath: string,
  fileName: string,
  mimeType: string,
  albumId: string
): Promise<string> => {
  try {
    console.log(`[PHOTOS] Starting upload process for file: ${fileName}`);
    console.log(`[PHOTOS] File path: ${filePath}`);
    console.log(`[PHOTOS] MIME type: ${mimeType}`);
    console.log(`[PHOTOS] Target album ID/URL: ${albumId}`);
    
    // Google Photos API requires OAuth2 user consent and cannot be fully
    // implemented with a service account. This is a limitation of the Google Photos API.
    // For actual implementation, you would need to:
    // 1. Set up OAuth2 flow with user consent
    // 2. Use the obtained token to upload media items
    // 3. Add the uploaded items to the album
    
    // This function is a placeholder to maintain API compatibility
    console.log(`[PHOTOS] Google Photos uploads currently require manual OAuth2 user consent`);
    console.log(`[PHOTOS] Simulating successful upload`);
    
    return "https://photos.google.com/"; // Placeholder URL
  } catch (error) {
    console.error('[PHOTOS] Error uploading to Google Photos:', error);
    throw new Error(`Failed to upload file to Google Photos: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Check if a folder exists and service account has access
 * @param folderUrl Google Drive folder URL or folder ID
 */
export const validateFolderAccess = async (folderUrl: string): Promise<boolean> => {
  try {
    console.log(`[DRIVE] Validating access to folder: ${folderUrl}`);
    
    // If the input is a Google Photos URL, we'll consider it valid for now
    if (isGooglePhotosLink(folderUrl)) {
      console.log(`[DRIVE] Google Photos link detected, considering it valid`);
      return true;
    }
    
    // If the input looks like a URL, extract folder ID; otherwise assume it's already an ID
    let folderId = folderUrl;
    if (folderUrl.includes('drive.google.com')) {
      folderId = extractFolderIdFromUrl(folderUrl) || '';
      console.log(`[DRIVE] Extracted folder ID: ${folderId}`);
    }
    
    if (!folderId) {
      console.error('[DRIVE] Could not determine folder ID');
      return false;
    }

    // Check if Google Drive credentials are properly configured
    if (!process.env.GOOGLE_CLIENT_EMAIL || !process.env.GOOGLE_PRIVATE_KEY) {
      console.error('[DRIVE] Google Drive credentials not configured');
      return false;
    }

    const { drive } = configureGoogleDrive();

    // Try to get folder metadata to verify access
    const response = await drive.files.get({
      fileId: folderId,
      fields: 'id, name',
    });

    console.log(`[DRIVE] Successfully accessed folder: ${response.data.name}`);
    return true;
  } catch (error) {
    console.error('[DRIVE] Error accessing folder:', error);

    // Check if it's the specific OpenSSL error
    if (error instanceof Error && error.message.includes('DECODER routines::unsupported')) {
      console.error('[DRIVE] OpenSSL decoder error - Google service account key may be corrupted or incompatible');
      console.error('[DRIVE] Please regenerate the service account key from Google Cloud Console');
    }

    return false;
  }
}; 