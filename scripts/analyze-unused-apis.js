#!/usr/bin/env node

/**
 * Unused API Analysis Script
 * Analyzes the codebase to find unused or orphaned API endpoints
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'bold');
  console.log('='.repeat(60));
}

// Get all API routes
function getAllApiRoutes(dir = 'app/api', routes = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      getAllApiRoutes(fullPath, routes);
    } else if (item === 'route.ts' || item === 'route.js') {
      // Convert file path to API route
      const apiPath = '/' + dir.replace('app/', '').replace(/\[([^\]]+)\]/g, ':$1');
      routes.push({
        path: apiPath,
        file: fullPath,
        methods: getHttpMethods(fullPath)
      });
    }
  }
  
  return routes;
}

// Extract HTTP methods from route file
function getHttpMethods(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const methods = [];
    
    if (content.includes('export async function GET')) methods.push('GET');
    if (content.includes('export async function POST')) methods.push('POST');
    if (content.includes('export async function PUT')) methods.push('PUT');
    if (content.includes('export async function DELETE')) methods.push('DELETE');
    if (content.includes('export async function PATCH')) methods.push('PATCH');
    
    return methods;
  } catch (error) {
    return [];
  }
}

// Search for API usage in codebase
function searchForApiUsage(apiPath, searchDirs = ['app', 'components', 'lib', 'hooks']) {
  const usages = [];
  
  function searchInDir(dir) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        searchInDir(fullPath);
      } else if (item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx')) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          
          // Clean API path for searching (remove parameters)
          const searchPath = apiPath.replace(/:\w+/g, '');
          
          // Look for various patterns
          const patterns = [
            new RegExp(`['"\`]${searchPath}['"\`]`, 'g'),
            new RegExp(`['"\`]/api${searchPath.replace('/api', '')}['"\`]`, 'g'),
            new RegExp(`fetch\\s*\\(\\s*['"\`][^'"\`]*${searchPath.replace('/api', '')}`, 'g'),
            new RegExp(`axios\\.[a-z]+\\s*\\(\\s*['"\`][^'"\`]*${searchPath.replace('/api', '')}`, 'g')
          ];
          
          for (const pattern of patterns) {
            const matches = content.match(pattern);
            if (matches) {
              usages.push({
                file: fullPath,
                matches: matches.length,
                patterns: matches
              });
              break; // Don't count multiple patterns in same file
            }
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }
    }
  }
  
  for (const dir of searchDirs) {
    searchInDir(dir);
  }
  
  return usages;
}

// Analyze API routes
function analyzeApiRoutes() {
  logSection('🔍 API Route Analysis');
  
  const routes = getAllApiRoutes();
  const analysis = {
    total: routes.length,
    used: 0,
    unused: 0,
    suspicious: 0,
    routes: []
  };
  
  log(`Found ${routes.length} API routes`, 'blue');
  
  for (const route of routes) {
    const usages = searchForApiUsage(route.path);
    const isUsed = usages.length > 0;
    
    // Check for suspicious patterns (placeholder responses)
    const content = fs.readFileSync(route.file, 'utf8');
    const isSuspicious = content.includes('temporarily disabled') || 
                        content.includes('placeholder response') ||
                        content.includes('status: 501');
    
    const routeAnalysis = {
      path: route.path,
      file: route.file,
      methods: route.methods,
      used: isUsed,
      suspicious: isSuspicious,
      usages: usages.length,
      usageFiles: usages.map(u => u.file)
    };
    
    analysis.routes.push(routeAnalysis);
    
    if (isUsed) {
      analysis.used++;
    } else {
      analysis.unused++;
    }
    
    if (isSuspicious) {
      analysis.suspicious++;
    }
    
    // Log route status
    const status = isUsed ? '✅ USED' : '❌ UNUSED';
    const suspiciousFlag = isSuspicious ? ' ⚠️ SUSPICIOUS' : '';
    const usageCount = isUsed ? ` (${usages.length} usage${usages.length !== 1 ? 's' : ''})` : '';
    
    log(`${status}${suspiciousFlag} ${route.methods.join(',')} ${route.path}${usageCount}`, 
        isUsed ? 'green' : 'red');
  }
  
  return analysis;
}

// Generate detailed report
function generateReport(analysis) {
  logSection('📊 Analysis Summary');
  
  log(`Total API Routes: ${analysis.total}`, 'blue');
  log(`Used Routes: ${analysis.used}`, 'green');
  log(`Unused Routes: ${analysis.unused}`, 'red');
  log(`Suspicious Routes: ${analysis.suspicious}`, 'yellow');
  log(`Usage Rate: ${((analysis.used / analysis.total) * 100).toFixed(1)}%`, 'blue');
  
  // Unused routes
  if (analysis.unused > 0) {
    logSection('❌ Unused Routes (Candidates for Removal)');
    analysis.routes
      .filter(r => !r.used)
      .forEach(route => {
        log(`• ${route.methods.join(',')} ${route.path}`, 'red');
        log(`  File: ${route.file}`, 'yellow');
      });
  }
  
  // Suspicious routes
  if (analysis.suspicious > 0) {
    logSection('⚠️ Suspicious Routes (Placeholder/Disabled)');
    analysis.routes
      .filter(r => r.suspicious)
      .forEach(route => {
        log(`• ${route.methods.join(',')} ${route.path} ${route.used ? '(USED)' : '(UNUSED)'}`, 'yellow');
        log(`  File: ${route.file}`, 'yellow');
      });
  }
  
  // Save detailed report
  const reportPath = 'api-analysis-report.json';
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: analysis.total,
      used: analysis.used,
      unused: analysis.unused,
      suspicious: analysis.suspicious,
      usageRate: ((analysis.used / analysis.total) * 100).toFixed(1)
    },
    routes: analysis.routes,
    recommendations: generateRecommendations(analysis)
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\n📄 Detailed report saved to: ${reportPath}`, 'blue');
  
  return report;
}

// Generate recommendations
function generateRecommendations(analysis) {
  const recommendations = [];
  
  const unusedRoutes = analysis.routes.filter(r => !r.used);
  const suspiciousRoutes = analysis.routes.filter(r => r.suspicious);
  
  if (unusedRoutes.length > 0) {
    recommendations.push({
      type: 'removal',
      priority: 'high',
      description: `Remove ${unusedRoutes.length} unused API routes`,
      routes: unusedRoutes.map(r => r.path)
    });
  }
  
  if (suspiciousRoutes.length > 0) {
    recommendations.push({
      type: 'review',
      priority: 'medium',
      description: `Review ${suspiciousRoutes.length} suspicious routes with placeholder responses`,
      routes: suspiciousRoutes.map(r => r.path)
    });
  }
  
  return recommendations;
}

// Main execution
function main() {
  log('🚀 Starting API Route Analysis...', 'bold');
  
  try {
    const analysis = analyzeApiRoutes();
    const report = generateReport(analysis);
    
    if (analysis.unused > 0 || analysis.suspicious > 0) {
      log('\n⚠️ Issues found. Check the report for details.', 'yellow');
    } else {
      log('\n🎉 All API routes are being used!', 'green');
    }
    
    process.exit(0);
    
  } catch (error) {
    log(`\n💥 Analysis failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run analysis if called directly
if (require.main === module) {
  main();
}

module.exports = { analyzeApiRoutes, generateReport };
