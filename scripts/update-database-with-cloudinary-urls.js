const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local
require('dotenv').config({ path: path.join(process.cwd(), '.env.local') });

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function updateDatabaseWithCloudinaryUrls() {
  console.log('🚀 Starting database update with Cloudinary URLs...\n');

  try {
    // Read the URL mapping file created by the migration script
    const mappingPath = path.join(process.cwd(), 'cloudinary-url-mapping.json');
    
    if (!fs.existsSync(mappingPath)) {
      console.log('❌ URL mapping file not found. Please run the image migration script first.');
      return;
    }

    const urlMapping = JSON.parse(fs.readFileSync(mappingPath, 'utf8'));
    console.log(`📋 Found ${Object.keys(urlMapping).length} URL mappings\n`);

    let updatedCount = 0;
    let errorCount = 0;

    // Update blog_posts table
    console.log('📝 Updating blog_posts table...');
    const { data: blogs, error: blogsError } = await supabase
      .from('blog_posts')
      .select('id, featured_image_url');

    if (blogsError) {
      console.error('❌ Error fetching blog posts:', blogsError);
    } else {
      for (const blog of blogs) {
        if (blog.featured_image_url && blog.featured_image_url.startsWith('/images/')) {
          // Use the relative path directly as it matches the mapping format
          const cloudinaryUrl = urlMapping[blog.featured_image_url];

          if (cloudinaryUrl) {
            const { error: updateError } = await supabase
              .from('blog_posts')
              .update({ featured_image_url: cloudinaryUrl })
              .eq('id', blog.id);

            if (updateError) {
              console.error(`❌ Error updating blog post ${blog.id}:`, updateError);
              errorCount++;
            } else {
              console.log(`✅ Updated blog post ${blog.id}: ${blog.featured_image_url} → ${cloudinaryUrl}`);
              updatedCount++;
            }
          } else {
            console.log(`⚠️ No Cloudinary URL found for blog post ${blog.id}: ${blog.featured_image_url}`);
          }
        }
      }
    }

    // Update trips table
    console.log('\n🗺️ Updating trips table...');
    const { data: trips, error: tripsError } = await supabase
      .from('trips')
      .select('id, featured_image_url');

    if (tripsError) {
      console.error('❌ Error fetching trips:', tripsError);
    } else {
      for (const trip of trips) {
        if (trip.featured_image_url && trip.featured_image_url.startsWith('/images/')) {
          // Use the relative path directly as it matches the mapping format
          const cloudinaryUrl = urlMapping[trip.featured_image_url];

          if (cloudinaryUrl) {
            const { error: updateError } = await supabase
              .from('trips')
              .update({ featured_image_url: cloudinaryUrl })
              .eq('id', trip.id);

            if (updateError) {
              console.error(`❌ Error updating trip ${trip.id}:`, updateError);
              errorCount++;
            } else {
              console.log(`✅ Updated trip ${trip.id}: ${trip.featured_image_url} → ${cloudinaryUrl}`);
              updatedCount++;
            }
          } else {
            console.log(`⚠️ No Cloudinary URL found for trip ${trip.id}: ${trip.featured_image_url}`);
          }
        }
      }
    }

    // Update trip_photos_details table
    console.log('\n📸 Updating trip_photos_details table...');
    const { data: tripPhotos, error: tripPhotosError } = await supabase
      .from('trip_photos_details')
      .select('id, featured_image_url');

    if (tripPhotosError) {
      console.error('❌ Error fetching trip photos:', tripPhotosError);
    } else {
      for (const tripPhoto of tripPhotos) {
        if (tripPhoto.featured_image_url && tripPhoto.featured_image_url.startsWith('/images/')) {
          // Use the relative path directly as it matches the mapping format
          const cloudinaryUrl = urlMapping[tripPhoto.featured_image_url];

          if (cloudinaryUrl) {
            const { error: updateError } = await supabase
              .from('trip_photos_details')
              .update({ featured_image_url: cloudinaryUrl })
              .eq('id', tripPhoto.id);

            if (updateError) {
              console.error(`❌ Error updating trip photo ${tripPhoto.id}:`, updateError);
              errorCount++;
            } else {
              console.log(`✅ Updated trip photo ${tripPhoto.id}: ${tripPhoto.featured_image_url} → ${cloudinaryUrl}`);
              updatedCount++;
            }
          } else {
            console.log(`⚠️ No Cloudinary URL found for trip photo ${tripPhoto.id}: ${tripPhoto.featured_image_url}`);
          }
        }
      }
    }

    // Update team_members table
    console.log('\n👥 Updating team_members table...');
    const { data: teamMembers, error: teamMembersError } = await supabase
      .from('team_members')
      .select('id, image_url');

    if (teamMembersError) {
      console.error('❌ Error fetching team members:', teamMembersError);
    } else {
      for (const member of teamMembers) {
        if (member.image_url && member.image_url.startsWith('/images/')) {
          // Use the relative path directly as it matches the mapping format
          const cloudinaryUrl = urlMapping[member.image_url];

          if (cloudinaryUrl) {
            const { error: updateError } = await supabase
              .from('team_members')
              .update({ image_url: cloudinaryUrl })
              .eq('id', member.id);

            if (updateError) {
              console.error(`❌ Error updating team member ${member.id}:`, updateError);
              errorCount++;
            } else {
              console.log(`✅ Updated team member ${member.id}: ${member.image_url} → ${cloudinaryUrl}`);
              updatedCount++;
            }
          } else {
            console.log(`⚠️ No Cloudinary URL found for team member ${member.id}: ${member.image_url}`);
          }
        }
      }
    }

    console.log('\n📊 Database Update Summary:');
    console.log(`✅ Successfully updated: ${updatedCount} records`);
    console.log(`❌ Errors encountered: ${errorCount} records`);
    
    if (updatedCount > 0) {
      console.log('\n🎉 Database update completed successfully!');
      console.log('\nNext steps:');
      console.log('1. Test your application to ensure images are loading correctly');
      console.log('2. Once confirmed working, you can remove the local images from public/images/');
      console.log('3. Keep the company logo and placeholder images locally');
    }

  } catch (error) {
    console.error('❌ Error during database update:', error);
  }
}

// Run the update
updateDatabaseWithCloudinaryUrls();
