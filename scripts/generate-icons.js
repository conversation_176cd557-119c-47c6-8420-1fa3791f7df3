const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Icon sizes needed for PWA
const iconSizes = [
  { size: 72, name: 'icon-72x72.png' },
  { size: 96, name: 'icon-96x96.png' },
  { size: 128, name: 'icon-128x128.png' },
  { size: 144, name: 'icon-144x144.png' },
  { size: 152, name: 'icon-152x152.png' },
  { size: 192, name: 'icon-192x192.png' },
  { size: 384, name: 'icon-384x384.png' },
  { size: 512, name: 'icon-512x512.png' },
];

// Special icons
const specialIcons = [
  { size: 96, name: 'search-96x96.png' },
  { size: 96, name: 'bookings-96x96.png' },
  { size: 96, name: 'contact-96x96.png' },
];

// Favicon sizes
const faviconSizes = [
  { size: 16, name: 'favicon-16x16.png' },
  { size: 32, name: 'favicon-32x32.png' },
  { size: 180, name: 'apple-touch-icon.png' },
];

async function generateIcons() {
  try {
    console.log('Starting icon generation...');

    // Create icons directory if it doesn't exist
    const iconsDir = path.join(process.cwd(), 'public', 'icons');
    if (!fs.existsSync(iconsDir)) {
      fs.mkdirSync(iconsDir, { recursive: true });
      console.log('Created icons directory');
    }

    // Source logo path
    const logoPath = path.join(process.cwd(), 'public', 'images', 'positive7-logo.png');
    
    if (!fs.existsSync(logoPath)) {
      console.error('Logo file not found at:', logoPath);
      console.log('Please ensure positive7-logo.png exists in public/images/');
      return;
    }

    // Generate PWA icons
    console.log('Generating PWA icons...');
    for (const icon of iconSizes) {
      const outputPath = path.join(iconsDir, icon.name);
      await sharp(logoPath)
        .resize(icon.size, icon.size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .png()
        .toFile(outputPath);
      console.log(`Generated: ${icon.name}`);
    }

    // Generate special icons (using the same logo for now)
    console.log('Generating special icons...');
    for (const icon of specialIcons) {
      const outputPath = path.join(iconsDir, icon.name);
      await sharp(logoPath)
        .resize(icon.size, icon.size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .png()
        .toFile(outputPath);
      console.log(`Generated: ${icon.name}`);
    }

    // Generate favicon files
    console.log('Generating favicon files...');
    for (const icon of faviconSizes) {
      const outputPath = path.join(process.cwd(), 'public', icon.name);
      await sharp(logoPath)
        .resize(icon.size, icon.size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .png()
        .toFile(outputPath);
      console.log(`Generated: ${icon.name}`);
    }

    // Generate ICO favicon
    console.log('Generating favicon.ico...');
    const faviconIcoPath = path.join(process.cwd(), 'public', 'favicon.ico');
    await sharp(logoPath)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .png()
      .toFile(faviconIcoPath.replace('.ico', '.png'));
    
    // Rename to .ico (Sharp doesn't support ICO format directly)
    fs.renameSync(faviconIcoPath.replace('.ico', '.png'), faviconIcoPath);
    console.log('Generated: favicon.ico');

    // Generate SVG icon
    console.log('Creating SVG icon...');
    const svgContent = `
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <rect width="100" height="100" fill="#3B82F6"/>
  <text x="50" y="60" font-family="Arial, sans-serif" font-size="40" font-weight="bold" text-anchor="middle" fill="white">P7</text>
</svg>`.trim();
    
    const svgPath = path.join(process.cwd(), 'public', 'icon.svg');
    fs.writeFileSync(svgPath, svgContent);
    console.log('Generated: icon.svg');

    // Generate OG images
    console.log('Generating Open Graph images...');
    
    // OG Image (1200x630)
    const ogImagePath = path.join(process.cwd(), 'public', 'images', 'og-image.jpg');
    await sharp({
      create: {
        width: 1200,
        height: 630,
        channels: 3,
        background: { r: 59, g: 130, b: 246 }
      }
    })
    .composite([
      {
        input: await sharp(logoPath)
          .resize(200, 200, { fit: 'contain' })
          .png()
          .toBuffer(),
        top: 215,
        left: 100
      },
      {
        input: await sharp({
          text: {
            text: 'Positive7 Educational Tours',
            font: 'Arial',
            fontSize: 48,
            rgba: true
          }
        }).png().toBuffer(),
        top: 250,
        left: 350
      },
      {
        input: await sharp({
          text: {
            text: 'Educational tours and travel experiences for students',
            font: 'Arial',
            fontSize: 24,
            rgba: true
          }
        }).png().toBuffer(),
        top: 320,
        left: 350
      }
    ])
    .jpeg({ quality: 90 })
    .toFile(ogImagePath);
    console.log('Generated: og-image.jpg');

    // Twitter Image (same as OG for now)
    const twitterImagePath = path.join(process.cwd(), 'public', 'images', 'twitter-image.jpg');
    fs.copyFileSync(ogImagePath, twitterImagePath);
    console.log('Generated: twitter-image.jpg');

    console.log('\n✅ All icons generated successfully!');
    console.log('\nGenerated files:');
    console.log('- PWA icons in /public/icons/');
    console.log('- Favicon files in /public/');
    console.log('- Open Graph images in /public/images/');

  } catch (error) {
    console.error('Error generating icons:', error);
    process.exit(1);
  }
}

// Check if Sharp is available
try {
  require('sharp');
  generateIcons();
} catch (error) {
  console.error('Sharp is not installed. Please run: npm install sharp');
  console.log('This is a development dependency for generating icons.');
  process.exit(1);
}
