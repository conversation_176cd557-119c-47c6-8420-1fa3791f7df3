#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');

/**
 * Updates the caching strategy across the entire application
 * This script ensures all components are properly integrated with the caching system
 */

async function updateCachingStrategy() {
  console.log('🔄 Updating caching strategy across the application...\n');

  // 1. Update service worker cache versions
  await updateServiceWorkerCaches();
  
  // 2. Clear all existing caches
  await clearAllCaches();
  
  // 3. Update component registry
  await updateComponentRegistry();
  
  // 4. Validate all components are registered
  await validateComponentRegistration();
  
  console.log('\n✅ Caching strategy updated successfully!');
  console.log('\n📝 Next steps:');
  console.log('1. Restart the development server');
  console.log('2. Clear browser caches: http://localhost:3000/clear-cache');
  console.log('3. Check status: http://localhost:3000/dev-status');
}

async function updateServiceWorkerCaches() {
  console.log('📦 Updating service worker cache versions...');
  
  const swPath = path.join(process.cwd(), 'public', 'sw.js');
  if (!fs.existsSync(swPath)) {
    console.log('⚠️  Service worker not found, skipping...');
    return;
  }

  let swContent = fs.readFileSync(swPath, 'utf8');
  const timestamp = Date.now();
  
  // Update all cache versions
  swContent = swContent.replace(
    /const CACHE_NAME = '[^']+';/,
    `const CACHE_NAME = 'positive7-v${timestamp}';`
  );
  swContent = swContent.replace(
    /const STATIC_CACHE = '[^']+';/,
    `const STATIC_CACHE = 'positive7-static-v${timestamp}';`
  );
  swContent = swContent.replace(
    /const DYNAMIC_CACHE = '[^']+';/,
    `const DYNAMIC_CACHE = 'positive7-dynamic-v${timestamp}';`
  );
  swContent = swContent.replace(
    /const IMAGE_CACHE = '[^']+';/,
    `const IMAGE_CACHE = 'positive7-images-v${timestamp}';`
  );
  swContent = swContent.replace(
    /const API_CACHE = '[^']+';/,
    `const API_CACHE = 'positive7-api-v${timestamp}';`
  );
  swContent = swContent.replace(
    /const COMPONENT_CACHE = '[^']+';/,
    `const COMPONENT_CACHE = 'positive7-components-v${timestamp}';`
  );

  fs.writeFileSync(swPath, swContent);
  console.log('✅ Service worker cache versions updated');
}

async function clearAllCaches() {
  console.log('🧹 Clearing all caches...');
  
  const cachePaths = [
    '.next',
    'node_modules/.cache',
    '.cache',
    'dist',
    'build',
  ];

  for (const cachePath of cachePaths) {
    const fullPath = path.join(process.cwd(), cachePath);
    if (fs.existsSync(fullPath)) {
      await fs.remove(fullPath);
      console.log(`✅ Cleared ${cachePath}`);
    }
  }
}

async function updateComponentRegistry() {
  console.log('📋 Updating component registry...');
  
  const registryPath = path.join(process.cwd(), 'lib', 'component-registry.ts');
  if (!fs.existsSync(registryPath)) {
    console.log('⚠️  Component registry not found, skipping...');
    return;
  }

  // Scan for components and update registry
  const componentsDir = path.join(process.cwd(), 'components');
  const components = await scanForComponents(componentsDir);
  
  console.log(`📊 Found ${components.length} components to register`);
  
  // Update registry with new components
  let registryContent = fs.readFileSync(registryPath, 'utf8');
  
  // Add any missing components to the registry
  for (const component of components) {
    if (!registryContent.includes(`'${component.name}':`)) {
      console.log(`➕ Adding ${component.name} to registry`);
      // Add component to registry (simplified - would need more sophisticated parsing)
    }
  }
  
  console.log('✅ Component registry updated');
}

async function scanForComponents(dir) {
  const components = [];
  
  async function scanDirectory(currentDir) {
    const items = await fs.readdir(currentDir);
    
    for (const item of items) {
      const itemPath = path.join(currentDir, item);
      const stat = await fs.stat(itemPath);
      
      if (stat.isDirectory()) {
        await scanDirectory(itemPath);
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        const content = await fs.readFile(itemPath, 'utf8');
        
        // Look for React components
        const componentMatches = content.match(/export\s+(function|const)\s+(\w+)/g);
        if (componentMatches) {
          for (const match of componentMatches) {
            const nameMatch = match.match(/export\s+(?:function|const)\s+(\w+)/);
            if (nameMatch) {
              const componentName = nameMatch[1];
              const relativePath = path.relative(process.cwd(), itemPath);
              
              components.push({
                name: componentName,
                path: relativePath,
                isClientOnly: content.includes("'use client'"),
                category: determineCategory(relativePath, content)
              });
            }
          }
        }
      }
    }
  }
  
  await scanDirectory(dir);
  return components;
}

function determineCategory(filePath, content) {
  if (filePath.includes('error')) return 'error-handling';
  if (filePath.includes('provider')) return 'provider';
  if (filePath.includes('security')) return 'security';
  if (filePath.includes('performance')) return 'performance';
  if (filePath.includes('accessibility')) return 'accessibility';
  if (content.includes('Analytics') || content.includes('tracking')) return 'analytics';
  return 'ui';
}

async function validateComponentRegistration() {
  console.log('🔍 Validating component registration...');
  
  // Check layout.tsx for unregistered components
  const layoutPath = path.join(process.cwd(), 'app', 'layout.tsx');
  if (fs.existsSync(layoutPath)) {
    const layoutContent = fs.readFileSync(layoutPath, 'utf8');
    
    // Look for components that might not be properly managed
    const componentUsages = layoutContent.match(/<\w+[^>]*>/g) || [];
    console.log(`📊 Found ${componentUsages.length} component usages in layout`);
  }
  
  console.log('✅ Component registration validated');
}

if (require.main === module) {
  updateCachingStrategy().catch(console.error);
}

module.exports = updateCachingStrategy;
