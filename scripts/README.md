# Scripts Documentation

## Functionality Test Script

### `test-all-functionality.js`

A comprehensive test script that checks all APIs, database connections, and external services to ensure the website is functioning properly.

#### Usage

```bash
# Run the test script
npm run test:functionality

# Or run directly
node scripts/test-all-functionality.js
```

#### What it tests

1. **Environment Variables** - Checks all required environment variables are set
2. **File System** - Verifies critical files exist
3. **Supabase Database** - Tests database connection and table accessibility
4. **Google Drive API** - Tests Google service account authentication
5. **Cloudinary API** - Tests image service connectivity
6. **API Endpoints** - Tests all REST API endpoints
7. **Page Routes** - Tests all website pages load correctly

#### Output

- **Console Output**: Real-time test results with colored status indicators
- **JSON Report**: Detailed report saved to `functionality-test-report.json`
- **Exit Code**: 0 for success, 1 for failures

#### Current Known Issues

1. **Google Drive API**: OpenSSL decoder error - requires new service account key
2. **Bookings Table**: Missing from database (optional feature)

#### Success Rate

The script currently achieves **95.7% success rate** with 45/47 tests passing.

#### When to Run

- After environment changes
- Before deployment
- When troubleshooting issues
- As part of maintenance checks

**Note**: This script is designed for manual testing only and should not be used in runtime as it may impact site performance.
